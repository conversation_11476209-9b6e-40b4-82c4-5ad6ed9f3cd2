-- 警察通知系统客户端

local activeAlerts = {}

-- 接收警察通知
RegisterNetEvent('organ_trade:policeAlert', function(alertData)
    if not Config.PoliceAlert.enabled then
        return
    end
    
    -- 检查是否是警察
    ESX.TriggerServerCallback('organ_trade:isPolice', function(isPolice)
        if not isPolice then
            return
        end
        
        -- 显示通知
        lib.notify({
            title = alertData.title,
            description = string.format('%s\n📍 位置: %s', alertData.message, alertData.location),
            type = 'error',
            duration = 8000,
            icon = 'fa-solid fa-exclamation-triangle'
        })
        
        -- 播放警报音
        PlaySoundFrontend(-1, 'TIMER_STOP', 'HUD_MINI_GAME_SOUNDSET', 1)
        
        -- 创建地图标记
        CreatePoliceBlip(alertData)
        
        if Config.Debug then
            print(string.format('^1[警察通知] ^7收到通知: %s 在 %s', alertData.title, alertData.location))
        end
    end)
end)

-- 创建警察标记点
function CreatePoliceBlip(alertData)
    local blip = AddBlipForCoord(alertData.coords.x, alertData.coords.y, alertData.coords.z)
    
    -- 设置标记属性
    SetBlipSprite(blip, alertData.sprite)
    SetBlipDisplay(blip, 4)
    SetBlipScale(blip, 1.2)
    SetBlipColour(blip, alertData.color)
    SetBlipAsShortRange(blip, false)
    
    -- 设置标记名称
    BeginTextCommandSetBlipName('STRING')
    AddTextComponentString(alertData.title)
    EndTextCommandSetBlipName(blip)
    
    -- 开始闪烁
    SetBlipFlashes(blip, true)
    
    -- 存储警报信息
    local alertId = #activeAlerts + 1
    activeAlerts[alertId] = {
        blip = blip,
        playerId = alertData.playerId,
        startTime = GetGameTimer(),
        flashDuration = Config.PoliceAlert.blip_flash_duration,
        totalDuration = Config.PoliceAlert.blip_duration,
        isTracking = true
    }
    
    if Config.Debug then
        print(string.format('^3[警察标记] ^7创建标记点 ID: %d，持续时间: %d秒', alertId, Config.PoliceAlert.blip_duration / 1000))
    end
end

-- 更新警察标记点
CreateThread(function()
    while true do
        local currentTime = GetGameTimer()
        
        for alertId, alert in pairs(activeAlerts) do
            if DoesBlipExist(alert.blip) then
                local elapsedTime = currentTime - alert.startTime
                
                -- 停止闪烁
                if elapsedTime >= alert.flashDuration and alert.isTracking then
                    SetBlipFlashes(alert.blip, false)
                end
                
                -- 跟踪玩家位置
                if alert.isTracking and alert.playerId then
                    local targetPlayer = GetPlayerFromServerId(alert.playerId)
                    if targetPlayer ~= -1 then
                        local targetPed = GetPlayerPed(targetPlayer)
                        if DoesEntityExist(targetPed) then
                            local coords = GetEntityCoords(targetPed)
                            SetBlipCoords(alert.blip, coords.x, coords.y, coords.z)
                        end
                    end
                end
                
                -- 移除过期的标记
                if elapsedTime >= alert.totalDuration then
                    RemoveBlip(alert.blip)
                    activeAlerts[alertId] = nil
                    
                    if Config.Debug then
                        print(string.format('^2[警察标记] ^7移除过期标记点 ID: %d', alertId))
                    end
                end
            else
                activeAlerts[alertId] = nil
            end
        end
        
        Wait(1000) -- 每秒更新一次
    end
end)

-- 手动移除警报标记
function RemovePoliceAlert(alertId)
    if activeAlerts[alertId] and DoesBlipExist(activeAlerts[alertId].blip) then
        RemoveBlip(activeAlerts[alertId].blip)
        activeAlerts[alertId] = nil
        
        if Config.Debug then
            print(string.format('^2[警察标记] ^7手动移除标记点 ID: %d', alertId))
        end
    end
end

-- 清理所有警报标记
function ClearAllPoliceAlerts()
    for alertId, alert in pairs(activeAlerts) do
        if DoesBlipExist(alert.blip) then
            RemoveBlip(alert.blip)
        end
    end
    activeAlerts = {}
    
    if Config.Debug then
        print('^2[警察标记] ^7清理所有标记点')
    end
end

-- 资源停止时清理
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        ClearAllPoliceAlerts()
    end
end)

-- 玩家离开时清理相关标记
AddEventHandler('playerDropped', function(playerId)
    for alertId, alert in pairs(activeAlerts) do
        if alert.playerId == playerId then
            if DoesBlipExist(alert.blip) then
                RemoveBlip(alert.blip)
            end
            activeAlerts[alertId] = nil
        end
    end
end)

-- 导出函数
exports('RemovePoliceAlert', RemovePoliceAlert)
exports('ClearAllPoliceAlerts', ClearAllPoliceAlerts)
exports('GetActiveAlerts', function() return activeAlerts end)

print('^2[器官交易系统] ^7警察通知系统客户端已加载')
