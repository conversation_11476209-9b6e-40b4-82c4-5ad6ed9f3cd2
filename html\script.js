// 器官交易系统JavaScript

let currentTargetId = null;
let currentOrgans = {};
let currentOrganStatus = {};
let currentMode = 'extraction'; // 'extraction' 或 'repair'

// 监听NUI消息
window.addEventListener('message', function(event) {
    const data = event.data;
    
    switch(data.type) {
        case 'openOrganSelection':
            openOrganSelection(data.targetId, data.organs, data.organStatus, data.mode);
            break;
        case 'closeUI':
            closeAllUI();
            break;

        case 'showNotification':
            showNotification(data.message, data.notificationType, data.duration);
            break;
        case 'showCountdown':
            showCountdown(data.duration, data.text);
            break;
        case 'hideCountdown':
            hideCountdown();
            break;
        case 'updateStatus':
            updatePlayerStatus(data.status);
            break;
        case 'showOrganStatus':
            showOrganStatus(data.organs);
            break;
        case 'refreshOrganStatus':
            refreshOrganStatus(data.organStatus);
            break;
    }
});

// 打开器官选择界面
function openOrganSelection(targetId, organs, organStatus, mode = 'extraction') {
    currentTargetId = targetId;
    currentOrgans = organs;
    currentOrganStatus = organStatus;
    currentMode = mode;

    // 更新界面标题
    const title = document.querySelector('#organSelection h2');
    if (title) {
        title.textContent = mode === 'repair' ? '器官修复' : '器官提取';
    }

    // 更新器官列表
    updateOrganList();

    // 更新器官按钮状态
    updateOrganButtons();

    // 显示界面
    document.getElementById('organSelection').style.display = 'block';
}

// 更新器官列表
function updateOrganList() {
    const organList = document.getElementById('organList');
    organList.innerHTML = '';



    for (const [key, organ] of Object.entries(currentOrgans)) {
        // 处理不同数据类型的状态值
        const statusValue = currentOrganStatus[organ.item];
        let isAvailable = false;

        // 根据模式判断可用性
        if (currentMode === 'repair') {
            // 修复模式：只有缺失的器官(状态为0)才可用
            isAvailable = statusValue === 0;
            // console.log(`[修复模式调试] 器官 ${key}: 状态值=${statusValue}, isAvailable=${isAvailable}`); // 调试信息已注释
        } else {
            // 解剖模式：只有存在的器官(状态为1)才可用
            if (typeof statusValue === 'boolean') {
                isAvailable = statusValue;
            } else if (typeof statusValue === 'number') {
                isAvailable = statusValue === 1;
            } else if (typeof statusValue === 'string') {
                isAvailable = statusValue === '1' || statusValue.toLowerCase() === 'true';
            } else {
                isAvailable = statusValue && statusValue !== 0 && statusValue !== '0' && statusValue !== 'false';
            }
        }



        const organItem = document.createElement('div');
        organItem.className = `organ-item ${isAvailable ? '' : 'unavailable'}`;

        // 根据模式显示不同的价格和状态
        let priceText, statusText, statusIcon;
        if (currentMode === 'repair') {
            priceText = `需要物品: ${getOrganItemName(organ.item)}`;
            // 修复：修正显示逻辑 - 之前逻辑是反的
            // isAvailable=true 表示器官缺失(状态=0)，应该显示"缺失"
            // isAvailable=false 表示器官正常(状态=1)，应该显示"正常"
            statusText = isAvailable ? '缺失' : '正常';
            statusIcon = isAvailable ? '❌' : '✅';
        } else {
            priceText = `价值: $${organ.price.toLocaleString()}`;
            statusText = isAvailable ? '可提取' : '已缺失';
            statusIcon = isAvailable ? '✓' : '✗';
        }

        organItem.innerHTML = `
            <div class="organ-name">[${key}] ${organ.name}</div>
            <div class="organ-price">${priceText}</div>
            <div class="organ-status ${isAvailable ? 'available' : 'unavailable'}">
                ${statusIcon}
            </div>
        `;

        if (isAvailable) {
            organItem.style.cursor = 'pointer';
            organItem.onclick = () => {
                // console.log(`[器官列表] 点击器官: ${key}, 可用: ${isAvailable}`); // 调试信息已注释
                selectOrgan(key);
            };
        } else {
            // console.log(`[器官列表] 器官 ${key} 不可用, 状态: ${statusValue}, 模式: ${currentMode}`); // 调试信息已注释
        }

        organList.appendChild(organItem);
    }
}

// 更新器官按钮状态
function updateOrganButtons() {
    const organButtons = document.querySelectorAll('.organ-btn');
    organButtons.forEach(button => {
        const organKey = button.getAttribute('data-organ');
        const organ = currentOrgans[organKey];

        // 清除之前的事件监听器，防止重复绑定
        button.onmouseenter = null;
        button.onmouseleave = null;
        button.onclick = null;

        // 根据当前模式判断器官按钮的可用性
        let isButtonAvailable = false;
        if (organ) {
            const statusValue = currentOrganStatus[organ.item];
            if (currentMode === 'repair') {
                // 修复模式：只有缺失的器官(状态为0)才可用
                isButtonAvailable = statusValue === 0;
            } else {
                // 解剖模式：只有存在的器官(状态为1)才可用
                isButtonAvailable = statusValue === 1;
            }
        }

        if (isButtonAvailable) {
            button.classList.remove('disabled');
            button.onclick = () => {
                // 添加点击动画
                button.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    button.style.transform = '';
                }, 150);

                // 移除其他按钮的选中状态
                organButtons.forEach(btn => btn.classList.remove('selected'));

                // 添加选中状态
                button.classList.add('selected');

                selectOrgan(organKey);
            };

            // 添加悬停提示 - 确保事件正确绑定
            button.onmouseenter = (e) => {
                e.stopPropagation();
                showOrganDetailTooltip(button, organ, true);
            };
            button.onmouseleave = (e) => {
                e.stopPropagation();
                hideOrganTooltip();
            };
        } else {
            button.classList.add('disabled');

            // 为不可用器官添加提示
            button.onmouseenter = (e) => {
                e.stopPropagation();
                showOrganDetailTooltip(button, organ, false);
            };
            button.onmouseleave = (e) => {
                e.stopPropagation();
                hideOrganTooltip();
            };
        }
    });
}

// 刷新器官状态（不关闭界面）
function refreshOrganStatus(newOrganStatus) {
    // 检查数据是否有效
    if (!newOrganStatus || typeof newOrganStatus !== 'object') {
        return;
    }

    currentOrganStatus = newOrganStatus;

    // 重新更新器官列表和按钮状态
    updateOrganList();
    updateOrganButtons();

    // 直接更新每个器官项目的状态
    for (const [key, organ] of Object.entries(currentOrgans)) {
        const statusValue = currentOrganStatus[organ.item];

        // 根据当前模式判断可用性
        let isAvailable = false;
        if (currentMode === 'repair') {
            // 修复模式：只有缺失的器官(状态为0)才可用
            isAvailable = statusValue === 0;
        } else {
            // 解剖模式：只有存在的器官(状态为1)才可用
            isAvailable = statusValue === 1;
        }

        // 更新器官列表项
        const organItems = document.querySelectorAll('.organ-item');
        organItems.forEach(item => {
            const organName = item.querySelector('.organ-name');
            if (organName && organName.textContent.includes(organ.name)) {
                item.className = `organ-item ${isAvailable ? '' : 'unavailable'}`;
                const statusElement = item.querySelector('.organ-status');
                if (statusElement) {
                    statusElement.className = `organ-status ${isAvailable ? 'available' : 'unavailable'}`;

                    // 根据模式显示不同的状态图标和文本
                    if (currentMode === 'repair') {
                        statusElement.textContent = isAvailable ? '❌' : '✅';
                    } else {
                        statusElement.textContent = isAvailable ? '✓' : '✗';
                    }
                }
                if (isAvailable) {
                    item.style.cursor = 'pointer';
                    item.onclick = () => selectOrgan(key);
                } else {
                    item.style.cursor = 'not-allowed';
                    item.onclick = null;
                }
            }
        });

        // 更新器官按钮
        const button = document.querySelector(`[data-organ="${key}"]`);
        if (button) {
            if (isAvailable) {
                button.classList.remove('disabled');
            } else {
                button.classList.add('disabled');
            }
        }
    }
}

// 选择器官
function selectOrgan(organKey) {
    // console.log(`[器官选择] 点击器官: ${organKey}`); // 调试信息已注释
    // console.log(`[器官选择] 当前模式: ${currentMode}`); // 调试信息已注释
    // console.log(`[器官选择] 器官数据:`, currentOrgans[organKey]); // 调试信息已注释
    // console.log(`[器官选择] 器官状态:`, currentOrganStatus); // 调试信息已注释

    const organ = currentOrgans[organKey];

    if (currentMode === 'repair') {
        // 修复模式：只能选择缺失的器官
        const organStatus = currentOrganStatus[organ.item];
        // console.log(`[器官选择] 器官 ${organKey} 状态: ${organStatus} (需要为0才能修复)`); // 调试信息已注释

        if (!organ || organStatus !== 0) {
            // console.log(`[器官选择] 器官不可修复 - organ存在: ${!!organ}, 状态: ${organStatus}`); // 调试信息已注释

            // 给用户提示为什么不能点击
            if (!organ) {
                alert('器官数据错误');
            } else if (organStatus === 1) {
                alert('该器官是健康的，不需要修复');
            } else {
                alert(`器官状态异常: ${organStatus}`);
            }
            return;
        }

        // 高亮选中的器官
        document.querySelectorAll('.organ-btn').forEach(btn => {
            btn.classList.remove('selected');
        });

        const selectedButton = document.querySelector(`[data-organ="${organKey}"]`);
        if (selectedButton) {
            selectedButton.classList.add('selected');
        }

        // 修复确认对话框 - 使用ox_lib确认框
        const requiredItem = getOrganItemName(organ.item);
        const confirmData = {
            type: 'repair',
            title: '确认修复',
            message: `🏥 确认修复 ${organ.name}？\n\n🫀 需要物品: ${requiredItem}\n\n✅ 修复后器官将恢复正常功能`,
            organKey: organKey,
            organField: organ.item,
            targetId: currentTargetId
        };

        // 发送确认请求到Lua
        fetch(`https://${GetParentResourceName()}/showConfirmDialog`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(confirmData)
        }).then(response => {
            // console.log('[器官修复] 确认对话框请求已发送', response); // 调试信息已注释
        }).catch(error => {
            console.error('[器官修复] 确认对话框请求失败', error); // 错误信息保留
        });
    } else {
        // 解剖模式：只能选择存在的器官
        if (!organ || currentOrganStatus[organ.item] !== 1) {
            return;
        }

        // 高亮选中的器官
        document.querySelectorAll('.organ-btn').forEach(btn => {
            btn.classList.remove('selected');
        });

        const selectedButton = document.querySelector(`[data-organ="${organKey}"]`);
        if (selectedButton) {
            selectedButton.classList.add('selected');
        }

        // 解剖确认对话框 - 使用ox_lib确认框
        const confirmData = {
            type: 'extraction',
            title: '确认解剖',
            message: `🔪 确认解剖提取 ${organ.name}？\n\n💰 器官价值: $${organ.price.toLocaleString()}\n\n⚠️  注意：此操作不可逆转！`,
            organKey: organKey,
            targetId: currentTargetId
        };

        // 发送确认请求到Lua
        fetch(`https://${GetParentResourceName()}/showConfirmDialog`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(confirmData)
        }).then(response => {
            // console.log('[器官解剖] 确认对话框请求已发送', response); // 调试信息已注释
        }).catch(error => {
            console.error('[器官解剖] 确认对话框请求失败', error); // 错误信息保留
        });
    }
}

// 获取医用器官物品名称
function getMedicalOrganItem(organItem) {
    const medicalItems = {
        'organ_heart': 'medical_heart',
        'organ_liver': 'medical_liver',
        'organ_kidney': 'medical_kidney',
        'organ_lung': 'medical_lung',
        'organ_pancreas': 'medical_pancreas',
        'organ_spleen': 'medical_spleen'
    };
    return medicalItems[organItem] || organItem;
}

// 获取医用器官物品的显示名称
function getOrganItemName(organItem) {
    const medicalItem = getMedicalOrganItem(organItem);
    const itemNames = {
        'medical_heart': '医用心脏',
        'medical_liver': '医用肝脏',
        'medical_kidney': '医用肾脏',
        'medical_lung': '医用肺部',
        'medical_pancreas': '医用胰腺',
        'medical_spleen': '医用脾脏'
    };
    return itemNames[medicalItem] || medicalItem;
}



// 取消手术
function cancelSurgery() {
    const confirmData = {
        type: 'cancelSurgery',
        title: '取消手术',
        message: '🚫 确认取消解剖手术？',
        targetId: currentTargetId
    };

    // 发送确认请求到Lua
    fetch(`https://${GetParentResourceName()}/showConfirmDialog`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(confirmData)
    }).then(response => {
        // console.log('[取消手术] 确认对话框请求已发送', response); // 调试信息已注释
    }).catch(error => {
        console.error('[取消手术] 确认对话框请求失败', error); // 错误信息保留
    });
}

// 结束手术
function endSurgery() {
    const confirmData = {
        type: 'endSurgery',
        title: '结束手术',
        message: '✅ 确认结束解剖手术？\n\n受害者将能够发送求救信号。',
        targetId: currentTargetId
    };

    // 发送确认请求到Lua
    fetch(`https://${GetParentResourceName()}/showConfirmDialog`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(confirmData)
    }).then(response => {
        // console.log('[结束手术] 确认对话框请求已发送', response); // 调试信息已注释
    }).catch(error => {
        console.error('[结束手术] 确认对话框请求失败', error); // 错误信息保留
    });
}

// 关闭所有UI
function closeAllUI() {
    document.getElementById('organSelection').style.display = 'none';
    document.getElementById('countdown').style.display = 'none';
    document.getElementById('organStatus').style.display = 'none';

    // 清理选中状态
    document.querySelectorAll('.organ-btn').forEach(btn => {
        btn.classList.remove('selected');
        // 清理事件监听器
        btn.onmouseenter = null;
        btn.onmouseleave = null;
    });

    // 强制清理所有提示框
    hideOrganTooltip();

    // 额外保险：直接移除所有可能的提示框元素
    const allTooltips = document.querySelectorAll('.organ-tooltip, #current-organ-tooltip');
    allTooltips.forEach(tooltip => {
        if (tooltip.parentNode) {
            tooltip.remove();
        }
    });
}



// 显示通知
function showNotification(message, type, duration) {
    const container = document.getElementById('notifications');
    
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    container.appendChild(notification);
    
    // 自动移除通知
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, duration);
}

// 显示倒计时
function showCountdown(duration, text) {
    const countdown = document.getElementById('countdown');
    const countdownText = document.getElementById('countdownText');
    const countdownTime = document.getElementById('countdownTime');
    
    countdownText.textContent = text;
    countdown.style.display = 'block';
    
    let endTime = Date.now() + duration;
    
    const updateCountdown = () => {
        const remaining = Math.max(0, endTime - Date.now());
        const minutes = Math.floor(remaining / 60000);
        const seconds = Math.floor((remaining % 60000) / 1000);
        
        countdownTime.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        if (remaining > 0) {
            requestAnimationFrame(updateCountdown);
        } else {
            hideCountdown();
        }
    };
    
    updateCountdown();
}

// 隐藏倒计时
function hideCountdown() {
    document.getElementById('countdown').style.display = 'none';
}

// 更新玩家状态
function updatePlayerStatus(status) {
    const drugStatus = document.querySelector('#drugStatus .status-value');
    const surgeryStatus = document.querySelector('#surgeryStatus .status-value');
    const lifeStatus = document.querySelector('#lifeStatus .status-value');
    
    drugStatus.textContent = status.drugged ? '被迷晕' : '正常';
    drugStatus.style.color = status.drugged ? '#ff4444' : '#00ff00';
    
    surgeryStatus.textContent = status.inSurgery ? '手术中' : '无';
    surgeryStatus.style.color = status.inSurgery ? '#ff4444' : '#00ff00';
    
    lifeStatus.textContent = status.lifeExtended ? '延长中' : '正常';
    lifeStatus.style.color = status.lifeExtended ? '#ffff00' : '#00ff00';
}

// 显示器官状态
function showOrganStatus(organs) {
    const container = document.getElementById('organStatus');
    const content = document.getElementById('organStatusContent');
    
    content.innerHTML = '';
    
    for (const [organField, status] of Object.entries(organs)) {
        if (organField === 'id' || organField === 'identifier' || organField === 'last_updated') {
            continue;
        }

        // 处理不同数据类型的状态值
        let isHealthy = false;

        // 处理布尔值、数字和字符串类型
        if (typeof status === 'boolean') {
            isHealthy = status;
        } else if (typeof status === 'number') {
            isHealthy = status === 1;
        } else if (typeof status === 'string') {
            isHealthy = status === '1' || status.toLowerCase() === 'true';
        } else {
            // 默认情况下，如果值存在且不是 0、false、'0'、'false'，则认为是健康的
            isHealthy = status && status !== 0 && status !== '0' && status !== 'false';
        }

        // 获取器官名称
        let organName = organField;
        for (const organ of Object.values(currentOrgans)) {
            if (organ.item === organField) {
                organName = organ.name;
                break;
            }
        }

        // console.log(`[器官状态调试] ${organName} (${organField}): 原始值=${status} (类型: ${typeof status}), 健康=${isHealthy}`); // 调试信息已注释

        const statusItem = document.createElement('div');
        statusItem.className = 'organ-status-item';
        statusItem.innerHTML = `
            <span class="organ-status-name">${organName}</span>
            <span class="organ-status-indicator ${isHealthy ? 'healthy' : 'damaged'}">
                ${isHealthy ? '✓' : '✗'}
            </span>
        `;

        content.appendChild(statusItem);
    }
    
    container.style.display = 'block';
}

// 隐藏器官状态
function hideOrganStatus() {
    document.getElementById('organStatus').style.display = 'none';
}

// 获取器官信息
function getOrganInfo(organName) {
    const organData = {
        '心脏': {
            icon: '❤️',
            description: '人体循环系统的核心器官，负责血液循环'
        },
        '肝脏': {
            icon: '🫀',
            description: '人体最大的内脏器官，负责解毒和代谢'
        },
        '肾脏': {
            icon: '🫘',
            description: '泌尿系统的重要器官，负责过滤血液'
        },
        '肺部': {
            icon: '🫁',
            description: '呼吸系统的主要器官，负责气体交换'
        },
        '胰腺': {
            icon: '🥞',
            description: '消化系统器官，分泌胰岛素和消化酶'
        },
        '脾脏': {
            icon: '🟣',
            description: '免疫系统器官，负责过滤血液和免疫功能'
        }
    };

    return organData[organName] || {
        icon: '🔴',
        description: '重要的人体器官'
    };
}

// ESC键处理
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const organSelection = document.getElementById('organSelection');
        if (organSelection && organSelection.style.display !== 'none') {
            fetch(`https://${GetParentResourceName()}/closeUI`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({})
            });
        }
    }
});

// 全局鼠标移动监听器 - 确保提示框只在悬停器官按钮时显示
document.addEventListener('mousemove', function(event) {
    const organSelection = document.getElementById('organSelection');
    if (!organSelection || organSelection.style.display === 'none') {
        return;
    }

    // 检查鼠标是否在器官按钮上
    const target = event.target;
    const isOnOrganButton = target && target.classList && target.classList.contains('organ-btn');

    // 如果鼠标不在器官按钮上，隐藏提示框
    if (!isOnOrganButton) {
        const tooltip = document.getElementById('current-organ-tooltip');
        if (tooltip) {
            // 检查鼠标是否在提示框上（虽然提示框设置了pointer-events: none，但以防万一）
            const tooltipRect = tooltip.getBoundingClientRect();
            const mouseX = event.clientX;
            const mouseY = event.clientY;

            const isOnTooltip = mouseX >= tooltipRect.left && mouseX <= tooltipRect.right &&
                              mouseY >= tooltipRect.top && mouseY <= tooltipRect.bottom;

            if (!isOnTooltip) {
                hideOrganTooltip();
            }
        }
    }
});

// 获取资源名称
function GetParentResourceName() {
    // 从URL中提取资源名称
    const url = window.location.href;
    const match = url.match(/nui:\/\/([^\/]+)/);
    if (match) {
        return match[1];
    }

    // 备用方案
    return 'qiguan_tudou';
}

// 显示详细器官提示
function showOrganDetailTooltip(button, organ, isAvailable) {
    // 确保完全移除现有提示
    hideOrganTooltip();

    // 如果器官信息无效，不显示提示
    if (!organ || !organ.name) {
        return;
    }

    const tooltip = document.createElement('div');
    tooltip.className = isAvailable ? 'organ-tooltip' : 'organ-tooltip unavailable';
    tooltip.id = 'current-organ-tooltip'; // 添加ID便于查找和清理

    // 获取器官图标和描述
    const organInfo = getOrganInfo(organ.name);

    // 根据模式显示不同的状态和操作提示
    let status, statusColor, borderColor, priceText, actionText;

    if (currentMode === 'repair') {
        status = isAvailable ? '缺失' : '正常';
        statusColor = isAvailable ? '#ff4444' : '#00ff00';
        borderColor = isAvailable ? '#ff4444' : '#00ff00';
        priceText = `🫀 需要物品: ${getOrganItemName(organ.item)}`;
        actionText = isAvailable ? '🖱️ 点击进行修复' : '';
    } else {
        status = isAvailable ? '可以摘除' : '已被摘除';
        statusColor = isAvailable ? '#00ff00' : '#ff4444';
        borderColor = isAvailable ? '#00ff00' : '#ff4444';
        priceText = `💰 价值: $${organ.price.toLocaleString()}`;
        actionText = isAvailable ? '🖱️ 点击进行解剖' : '';
    }

    tooltip.innerHTML = `
        <div style="font-size: 16px; font-weight: bold; margin-bottom: 8px; color: #ffffff;">
            ${organInfo.icon} ${organ.name}
        </div>
        <div style="font-size: 12px; margin-bottom: 8px; color: #aaaaaa; line-height: 1.3;">
            ${organInfo.description}
        </div>
        <div style="font-size: 14px; margin-bottom: 6px; color: #cccccc;">
            ${priceText}
        </div>
        <div style="font-size: 14px; color: ${statusColor}; font-weight: bold;">
            ${isAvailable ? '✅' : '❌'} ${status}
        </div>
        ${actionText ? `<div style="font-size: 12px; color: #ffff00; margin-top: 6px;">${actionText}</div>` : ''}
    `;

    tooltip.style.cssText = `
        position: absolute;
        background: rgba(0, 0, 0, 0.95);
        color: white;
        padding: 12px 16px;
        border-radius: 8px;
        font-size: 14px;
        pointer-events: none;
        z-index: 10000;
        white-space: nowrap;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
        border: 2px solid ${borderColor};
        backdrop-filter: blur(5px);
        opacity: 0;
        transform: scale(0.9);
        transition: all 0.3s ease;
        overflow: hidden;
    `;

    document.body.appendChild(tooltip);

    // 定位提示框
    const rect = button.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();

    // 计算位置，确保不超出屏幕边界
    let left = rect.right + 15; // 显示在按钮右侧
    let top = rect.top + rect.height / 2 - tooltipRect.height / 2;

    // 边界检查
    if (left + tooltipRect.width > window.innerWidth - 10) {
        left = rect.left - tooltipRect.width - 15; // 显示在左侧
    }
    if (top < 10) top = 10;
    if (top + tooltipRect.height > window.innerHeight - 10) {
        top = window.innerHeight - tooltipRect.height - 10;
    }

    tooltip.style.left = left + 'px';
    tooltip.style.top = top + 'px';

    // 添加淡入动画
    requestAnimationFrame(() => {
        tooltip.style.opacity = '1';
        tooltip.style.transform = 'scale(1)';
    });
}

// 显示简单器官提示（备用函数）
function showOrganTooltip(button, organName) {
    showOrganDetailTooltip(button, {name: organName, price: 0}, true);
}

// 隐藏器官提示
function hideOrganTooltip() {
    // 通过ID查找当前提示框
    const tooltip = document.getElementById('current-organ-tooltip');
    if (tooltip) {
        tooltip.style.opacity = '0';
        tooltip.style.transform = 'scale(0.9)';
        setTimeout(() => {
            if (tooltip.parentNode) {
                tooltip.remove();
            }
        }, 300);
    }

    // 额外清理：移除所有可能残留的提示框
    const allTooltips = document.querySelectorAll('.organ-tooltip');
    allTooltips.forEach(tip => {
        if (tip.parentNode) {
            tip.remove();
        }
    });
}
