-- 警察通知系统服务器端

-- 发送警察通知
function SendPoliceAlert(alertType, playerId, coords, extraData)
    if Config.Debug then
        print(string.format('^3[警察通知] ^7SendPoliceAlert 被调用 - 类型: %s, 玩家ID: %s', alertType, playerId))
    end

    if not Config.PoliceAlert.enabled then
        if Config.Debug then
            print('^1[警察通知] ^7警察通知系统已禁用')
        end
        return
    end

    local alertConfig = Config.PoliceAlert[alertType]
    if not alertConfig or not alertConfig.enabled then
        if Config.Debug then
            print(string.format('^1[警察通知] ^7警报类型 %s 未启用或不存在', alertType))
        end
        return
    end
    
    local xPlayer = ESX.GetPlayerFromId(playerId)
    if not xPlayer then
        return
    end
    
    -- 获取位置信息
    local location = string.format('%.0f, %.0f', coords.x, coords.y)
    if extraData and extraData.location then
        location = extraData.location
    end
    
    -- 构建警报数据
    local alertData = {
        title = alertConfig.title,
        message = alertConfig.message,
        location = location,
        coords = coords,
        playerId = playerId,
        playerName = xPlayer.getName(),
        sprite = alertConfig.blip_sprite,
        color = alertConfig.blip_color,
        timestamp = os.time()
    }
    
    -- 添加额外信息
    if extraData then
        if extraData.organName then
            alertData.message = alertData.message .. string.format(' (%s)', extraData.organName)
        end
        if extraData.quantity and extraData.quantity > 1 then
            alertData.message = alertData.message .. string.format(' x%d', extraData.quantity)
        end
    end
    
    -- 获取所有在线警察
    local policeCount = 0
    local xPlayers = ESX.GetExtendedPlayers()
    
    for _, xPolice in pairs(xPlayers) do
        if IsPlayerPolice(xPolice.source) then
            policeCount = policeCount + 1
            TriggerClientEvent('organ_trade:policeAlert', xPolice.source, alertData)
        end
    end
    
    -- 记录日志
    LogAction('POLICE_ALERT', playerId, nil, string.format('%s - %s - Notified %d officers', alertType, alertData.message, policeCount))
    
    if Config.Debug then
        print(string.format('^1[警察通知] ^7%s - 玩家: %s, 位置: %s, 通知警察: %d人', 
            alertData.title, xPlayer.getName(), location, policeCount))
    end
end

-- 检查玩家是否是警察
function IsPlayerPolice(source)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then
        return false
    end
    
    local playerJob = xPlayer.job.name
    
    for _, job in ipairs(Config.PoliceAlert.jobs) do
        if playerJob == job then
            return true
        end
    end
    
    return false
end

-- ESX回调：检查是否是警察
ESX.RegisterServerCallback('organ_trade:isPolice', function(source, cb)
    cb(IsPlayerPolice(source))
end)

-- 解剖开始时发送警报
RegisterNetEvent('organ_trade:surgeryStarted', function(surgeonId)
    if Config.Debug then
        print(string.format('^3[警察通知] ^7收到解剖开始事件，外科医生ID: %s', surgeonId))
    end

    local coords = GetPlayerCoords(surgeonId)

    SendPoliceAlert('surgery_alert', surgeonId, coords, {
        location = '解剖现场'
    })
end)

-- 器官提取时发送警报
RegisterNetEvent('organ_trade:organExtracted', function(surgeonId, organKey)
    if Config.Debug then
        print(string.format('^3[警察通知] ^7收到器官提取事件，外科医生ID: %s, 器官: %s', surgeonId, organKey))
    end

    local coords = GetPlayerCoords(surgeonId)

    -- 获取器官名称
    local organName = '未知器官'
    local organ = Config.Surgery.organs[organKey]
    if organ then
        organName = organ.name
    end

    SendPoliceAlert('surgery_alert', surgeonId, coords, {
        location = '解剖现场',
        organName = organName
    })
end)

-- 器官出售时发送警报
RegisterNetEvent('organ_trade:organSold', function(playerId, organItem, quantity)
    if Config.Debug then
        print(string.format('^3[警察通知] ^7收到器官出售事件，玩家ID: %s, 器官: %s, 数量: %s', playerId, organItem, quantity))
    end

    local coords = GetPlayerCoords(playerId)

    -- 获取器官名称
    local organName = '未知器官'
    for _, organ in pairs(Config.Surgery.organs) do
        if organ.item == organItem then
            organName = organ.name
            break
        end
    end

    SendPoliceAlert('organ_sale_alert', playerId, coords, {
        location = '黑市交易点',
        organName = organName,
        quantity = quantity
    })
end)

-- 管理员命令：清理所有警报
RegisterCommand('clearpolice', function(source, args, rawCommand)
    if source == 0 then -- 控制台
        TriggerClientEvent('organ_trade:clearAllPoliceAlerts', -1)
        if Config.Debug then
            print('^2[警察通知] ^7已清理所有警报标记')
        end
    else
        local xPlayer = ESX.GetPlayerFromId(source)
        if xPlayer and xPlayer.getGroup() == 'admin' then
            TriggerClientEvent('organ_trade:clearAllPoliceAlerts', -1)
            NotifyPlayer(source, '已清理所有警报标记', 'success')
        else
            NotifyPlayer(source, '权限不足', 'error')
        end
    end
end, false)

-- 导出函数
exports('SendPoliceAlert', SendPoliceAlert)
exports('IsPlayerPolice', IsPlayerPolice)

print('^2[器官交易系统] ^7警察通知系统服务器端已加载')
