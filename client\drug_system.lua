-- 迷药系统客户端

local isDrugged = false
local drugThread = nil
local screenEffectThread = nil

-- 器官缺失状态管理
local hasOrganDeficiency = false
local organDeficiencyThread = nil
local missingOrgans = {}
local organDeficiencyCheckInterval = 30000 -- 30秒检查一次

-- 应用迷药效果
RegisterNetEvent('organ_trade:applyDrugEffect', function(duration)
    if isDrugged then return end

    isDrugged = true
    local playerPed = PlayerPedId()

    -- 通知玩家被下药
    lib.notify({
        title = '器官交易系统',
        description = '你感到头晕目眩...',
        type = 'error',
        duration = 5000
    })

    -- 开始倒地效果
    StartKnockdownEffect()

    -- 开始屏幕效果
    StartScreenEffects()

    -- 设置效果持续时间
    CreateThread(function()
        Wait(duration)
        RemoveDrugEffect()
    end)
end)

-- 移除迷药效果
RegisterNetEvent('organ_trade:removeDrugEffect', function()
    RemoveDrugEffect()
end)

-- 移除迷药效果函数
function RemoveDrugEffect()
    if not isDrugged then return end

    isDrugged = false

    -- 停止倒地效果
    StopKnockdownEffect()

    -- 停止屏幕效果
    StopScreenEffects()

    lib.notify({
        title = '器官交易系统',
        description = '药效已消退...',
        type = 'info'
    })

    -- 检查器官缺失状态，如果有缺失器官则继续保持受伤状态
    CreateThread(function()
        Wait(1000) -- 等待1秒让迷药效果完全清除
        CheckOrganDeficiency()
    end)
end

-- 开始倒地效果
function StartKnockdownEffect()
    local playerPed = PlayerPedId()

    -- 主要使用受伤扭动动画
    RequestAnimDict("combat@damage@writhe")
    while not HasAnimDictLoaded("combat@damage@writhe") do
        Wait(100)
    end

    -- 让玩家倒地并扭动（受伤状态）
    TaskPlayAnim(playerPed, "combat@damage@writhe", "writhe_loop", 8.0, -8.0, -1, 1, 0, false, false, false)

    

    -- 创建持续效果线程
    drugThread = CreateThread(function()
        while isDrugged do
            local playerPed = PlayerPedId()

            -- 禁用玩家控制
            DisableControlAction(0, 21, true) -- 冲刺
            DisableControlAction(0, 22, true) -- 跳跃
            DisableControlAction(0, 23, true) -- 进入载具
            DisableControlAction(0, 75, true) -- 离开载具
            DisableControlAction(0, 36, true) -- 蹲下
            DisableControlAction(0, 44, true) -- 掩护

            -- 如果玩家试图站起来，强制让他们保持受伤扭动状态
            if not IsEntityPlayingAnim(playerPed, "combat@damage@writhe", "writhe_loop", 3) and
               not IsEntityPlayingAnim(playerPed, "combat@damage@injured_pistol", "injured_pistol_loop", 3) then

                -- 优先播放受伤扭动动画
                if HasAnimDictLoaded("combat@damage@writhe") then
                    TaskPlayAnim(playerPed, "combat@damage@writhe", "writhe_loop", 8.0, -8.0, -1, 1, 0, false, false, false)
                elseif HasAnimDictLoaded("combat@damage@injured_pistol") then
                    TaskPlayAnim(playerPed, "combat@damage@injured_pistol", "injured_pistol_loop", 8.0, -8.0, -1, 1, 0, false, false, false)
                end
            end

            -- 设置玩家生命值不会降到0以下（防止真正死亡）
            local health = GetEntityHealth(playerPed)
            if health < 200 then
                SetEntityHealth(playerPed, 200)
            end

            Wait(100)
        end
    end)

    -- 额外的通知
    CreateThread(function()
        Wait(2000)
        if isDrugged then
            lib.notify({
                title = '器官交易系统',
                description = '你无法站起来...',
                type = 'error',
                duration = 3000
            })
        end
    end)
end

-- 停止倒地效果
function StopKnockdownEffect()
    if drugThread then
        drugThread = nil
    end

    local playerPed = PlayerPedId()

    -- 停止所有受伤扭动动画
    StopAnimTask(playerPed, "combat@damage@writhe", "writhe_loop", 3.0)
    StopAnimTask(playerPed, "combat@damage@injured_pistol", "injured_pistol_loop", 3.0)

    -- 清除所有任务让玩家站起来
    ClearPedTasks(playerPed)
    ClearPedTasksImmediately(playerPed)

    -- 播放站起来的动画
    RequestAnimDict("get_up@directional@movement@from_knees@action")
    CreateThread(function()
        while not HasAnimDictLoaded("get_up@directional@movement@from_knees@action") do
            Wait(100)
        end
        TaskPlayAnim(playerPed, "get_up@directional@movement@from_knees@action", "getup_r_0", 8.0, -8.0, 3000, 0, 0, false, false, false)

        -- 清理动画字典
        Wait(3000)
        RemoveAnimDict("combat@damage@writhe")
        RemoveAnimDict("combat@damage@injured_pistol")
        RemoveAnimDict("get_up@directional@movement@from_knees@action")
    end)
end

-- 传送玩家
RegisterNetEvent('organ_trade:teleportPlayer', function(coords)
    if not isDrugged then return end

    local playerPed = PlayerPedId()
    SetEntityCoords(playerPed, coords.x, coords.y, coords.z, false, false, false, true)

    lib.notify({
        title = '器官交易系统',
        description = '你被带到了一个陌生的地方...',
        type = 'error'
    })
end)





-- 开始迷药选择流程
RegisterNetEvent('organ_trade:startDrugSelection', function()
    UseDrugItem()
end)

-- 使用迷药道具
function UseDrugItem()
    -- 检查自己是否被迷晕
    if isDrugged then
        lib.notify({
            title = '器官交易系统',
            description = Config.Notifications.user_drugged_cannot_use,
            type = 'error'
        })
        return
    end

    -- 获取附近玩家
    TriggerServerEvent('organ_trade:getNearbyPlayers')
end

-- 接收附近玩家列表
RegisterNetEvent('organ_trade:receiveNearbyPlayers', function(players)
    if #players == 0 then
        lib.notify({
            title = '器官交易系统',
            description = '附近没有玩家',
            type = 'error'
        })
        return
    end

    -- 创建玩家选择菜单
    local options = {}

    for _, player in ipairs(players) do
        local title = string.format('%s (距离: %.1fm)', player.name, player.distance)
        local description = '选择此玩家使用迷药'

        -- 如果玩家已被迷晕，修改显示和描述
        if player.isDrugged then
            title = title .. ' [已被迷晕]'
            description = '该玩家已被迷晕，无法重复使用迷药'
        end

        table.insert(options, {
            title = title,
            description = description,
            disabled = player.isDrugged, -- 禁用已被迷晕的玩家选项
            onSelect = function()
                if player.isDrugged then
                    lib.notify({
                        title = '器官交易系统',
                        description = '该玩家已被迷晕，无法重复使用迷药',
                        type = 'error'
                    })
                    return
                end

                -- 确认对话框
                local alert = lib.alertDialog({
                    header = '确认使用迷药',
                    content = '确认对此玩家使用迷药？',
                    centered = true,
                    cancel = true
                })

                if alert == 'confirm' then
                    TriggerServerEvent('organ_trade:useDrug', player.id)
                end
            end
        })
    end

    lib.registerContext({
        id = 'drug_target_menu',
        title = '选择目标',
        options = options
    })

    lib.showContext('drug_target_menu')
end)

-- 检查迷药状态
function CheckDrugStatus()
    TriggerServerEvent('organ_trade:checkDrugStatus')
end

-- 接收迷药状态响应
RegisterNetEvent('organ_trade:drugStatusResponse', function(drugged)
    isDrugged = drugged
end)

-- 玩家生成时检查状态
AddEventHandler('playerSpawned', function()
    CreateThread(function()
        Wait(2000) -- 等待ESX加载
        CheckDrugStatus()
    end)
end)

-- 开始屏幕效果
function StartScreenEffects()
    -- 应用屏幕模糊效果
    SetTimecycleModifier('spectator5')
    SetTimecycleModifierStrength(0.8)

    -- 开始屏幕晃动和视觉效果线程
    screenEffectThread = CreateThread(function()
        local shakeIntensity = 0.5
        local blurStrength = 0.8
        local effectTimer = 0

        while isDrugged do
            -- 屏幕晃动效果
            ShakeGameplayCam('DRUNK_SHAKE', shakeIntensity)

            -- 动态调整效果强度
            effectTimer = effectTimer + 1

            -- 每5秒变化一次强度，模拟药效波动
            if effectTimer % 500 == 0 then
                shakeIntensity = math.random(30, 80) / 100  -- 0.3-0.8之间随机
                blurStrength = math.random(60, 100) / 100   -- 0.6-1.0之间随机
                SetTimecycleModifierStrength(blurStrength)
            end

            -- 添加随机的强烈晃动
            if math.random(1, 200) == 1 then
                ShakeGameplayCam('VIBRATE_SHAKE', 1.5)
                Wait(500)
            end

            -- 模拟视觉扭曲
            if math.random(1, 300) == 1 then
                SetTimecycleModifier('drug_flying_base')
                Wait(2000)
                if isDrugged then
                    SetTimecycleModifier('spectator5')
                end
            end

            Wait(10)
        end
    end)
end

-- 停止屏幕效果
function StopScreenEffects()
    if screenEffectThread then
        screenEffectThread = nil
    end

    -- 停止摄像头晃动
    StopGameplayCamShaking(true)

    -- 清除时间周期修改器
    ClearTimecycleModifier()

    -- 渐进式恢复正常视觉
    CreateThread(function()
        local strength = 0.8
        while strength > 0 do
            SetTimecycleModifier('spectator5')
            SetTimecycleModifierStrength(strength)
            strength = strength - 0.05
            Wait(100)
        end
        ClearTimecycleModifier()
    end)
end

-- 检查器官缺失状态
function CheckOrganDeficiency()
    -- 请求服务器检查当前玩家的器官状态
    TriggerServerEvent('organ_trade:checkPlayerOrganDeficiency')
end

-- 接收器官缺失状态
RegisterNetEvent('organ_trade:receiveOrganDeficiency', function(organStatus, missingOrgansList)
    local hasMissingOrgans = #missingOrgansList > 0

    if hasMissingOrgans and not hasOrganDeficiency then
        -- 开始器官缺失效果
        StartOrganDeficiencyEffect(missingOrgansList)
    elseif not hasMissingOrgans and hasOrganDeficiency then
        -- 停止器官缺失效果
        StopOrganDeficiencyEffect()
    elseif hasMissingOrgans and hasOrganDeficiency then
        -- 更新缺失器官列表
        missingOrgans = missingOrgansList
        ShowOrganDeficiencyNotification()
    end
end)

-- 开始器官缺失效果
function StartOrganDeficiencyEffect(missingOrgansList)
    if hasOrganDeficiency then return end

    hasOrganDeficiency = true
    missingOrgans = missingOrgansList

    local playerPed = PlayerPedId()

    -- 显示器官缺失通知
    ShowOrganDeficiencyNotification()

    -- 开始受伤动画
    RequestAnimDict("combat@damage@writhe")
    while not HasAnimDictLoaded("combat@damage@writhe") do
        Wait(100)
    end

    TaskPlayAnim(playerPed, "combat@damage@writhe", "writhe_loop", 8.0, -8.0, -1, 1, 0, false, false, false)

    -- 创建持续效果线程
    organDeficiencyThread = CreateThread(function()
        while hasOrganDeficiency do
            local playerPed = PlayerPedId()

            -- 禁用大部分控制
            DisableControlAction(0, 21, true) -- 冲刺
            DisableControlAction(0, 22, true) -- 跳跃
            DisableControlAction(0, 23, true) -- 进入载具
            DisableControlAction(0, 75, true) -- 离开载具
            DisableControlAction(0, 140, true) -- 近战攻击
            DisableControlAction(0, 141, true) -- 近战攻击
            DisableControlAction(0, 142, true) -- 近战攻击
            DisableControlAction(0, 143, true) -- 近战攻击
            DisableControlAction(0, 263, true) -- 近战攻击
            DisableControlAction(0, 264, true) -- 近战攻击
            DisableControlAction(0, 257, true) -- 攻击
            DisableControlAction(0, 25, true) -- 瞄准

            -- 强制保持受伤状态
            if not IsEntityPlayingAnim(playerPed, "combat@damage@writhe", "writhe_loop", 3) then
                TaskPlayAnim(playerPed, "combat@damage@writhe", "writhe_loop", 8.0, -8.0, -1, 1, 0, false, false, false)
            end

            -- 设置玩家血量上限（模拟器官缺失的虚弱状态）
            local maxHealth = 200 - (#missingOrgans * 30) -- 每缺失一个器官减少30点血量上限
            if maxHealth < 50 then maxHealth = 50 end -- 最低50血量

            if GetEntityHealth(playerPed) > maxHealth then
                SetEntityHealth(playerPed, maxHealth)
            end

            -- 每30秒显示一次提示
            if GetGameTimer() % organDeficiencyCheckInterval < 100 then
                ShowOrganDeficiencyNotification()
            end

            Wait(100)
        end
    end)
end

-- 停止器官缺失效果
function StopOrganDeficiencyEffect()
    if not hasOrganDeficiency then return end

    hasOrganDeficiency = false
    missingOrgans = {}

    -- 停止线程
    if organDeficiencyThread then
        organDeficiencyThread = nil
    end

    -- 停止动画
    local playerPed = PlayerPedId()
    ClearPedTasks(playerPed)

    -- 恢复正常血量上限
    SetEntityHealth(playerPed, GetEntityMaxHealth(playerPed))

    lib.notify({
        title = '器官交易系统',
        description = '所有器官已修复，身体状况恢复正常',
        type = 'success',
        duration = 5000
    })
end

-- 显示器官缺失通知
function ShowOrganDeficiencyNotification()
    if #missingOrgans == 0 then return end

    local organNames = {
        organ_heart = '心脏',
        organ_liver = '肝脏',
        organ_kidney = '肾脏',
        organ_lung = '肺部',
        organ_pancreas = '胰腺',
        organ_spleen = '脾脏'
    }

    local missingOrganNames = {}
    for _, organ in ipairs(missingOrgans) do
        table.insert(missingOrganNames, organNames[organ] or organ)
    end

    local message = string.format('⚠️ 器官缺失：%s\n\n你因为器官缺失而无法正常行动，需要医疗救助！', table.concat(missingOrganNames, '、'))

    lib.notify({
        title = '器官缺失警告',
        description = message,
        type = 'error',
        duration = 8000
    })
end

-- 玩家初始化时检查器官状态
AddEventHandler('esx:playerLoaded', function()
    CreateThread(function()
        Wait(5000) -- 等待5秒确保数据加载完成
        CheckOrganDeficiency()

        -- 定期检查器官状态
        while true do
            Wait(organDeficiencyCheckInterval)
            if not isDrugged then -- 只有在非迷药状态下才检查
                CheckOrganDeficiency()
            end
        end
    end)
end)

-- 资源启动时也检查一次
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        CreateThread(function()
            Wait(2000)
            CheckOrganDeficiency()
        end)
    end
end)

-- 导出函数
exports('UseDrugItem', UseDrugItem)
exports('IsDrugged', function() return isDrugged end)
exports('HasOrganDeficiency', function() return hasOrganDeficiency end)
exports('GetMissingOrgans', function() return missingOrgans end)

print('^2[器官交易系统] ^7迷药系统客户端已加载')
