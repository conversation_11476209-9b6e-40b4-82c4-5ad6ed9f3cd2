-- 解剖系统客户端

local isInSurgery = false
local surgeryBlip = nil

-- 使用手术刀
function UseSurgeryKnife()
    -- 检查自己是否被迷晕
    if isDrugged then
        lib.notify({
            title = '器官交易系统',
            description = Config.Notifications.user_drugged_cannot_use,
            type = 'error'
        })
        return
    end

    -- 获取附近被迷晕的玩家
    TriggerServerEvent('organ_trade:getNearbyDruggedPlayers')
end

-- 接收附近被迷晕的玩家
RegisterNetEvent('organ_trade:receiveNearbyDruggedPlayers', function(players)
    if #players == 0 then
        lib.notify({
            title = '器官交易系统',
            description = '附近没有被迷晕的玩家',
            type = 'error'
        })
        return
    end

    -- 创建玩家选择菜单
    local options = {}

    for _, player in ipairs(players) do
        table.insert(options, {
            title = string.format('%s (距离: %.1fm)', player.name, player.distance),
            description = '选择此玩家进行解剖',
            onSelect = function()
                -- 确认对话框
                local alert = lib.alertDialog({
                    header = '确认解剖',
                    content = '确认开始解剖手术？',
                    centered = true,
                    cancel = true
                })

                if alert == 'confirm' then
                    TriggerServerEvent('organ_trade:startSurgery', player.id)
                end
            end
        })
    end

    lib.registerContext({
        id = 'surgery_target_menu',
        title = '选择解剖目标',
        options = options
    })

    lib.showContext('surgery_target_menu')
end)

-- 打开器官选择界面
RegisterNetEvent('organ_trade:openOrganSelection', function(targetId)
    -- 先获取目标玩家的器官状态
    TriggerServerEvent('organ_trade:getPlayerOrganStatus', targetId)
    
    -- 等待器官状态响应
    local organStatus = nil
    local handler = nil
    
    handler = RegisterNetEvent('organ_trade:receiveOrganStatus', function(organs)
        organStatus = organs
        RemoveEventHandler(handler)
    end)
    
    -- 等待响应
    CreateThread(function()
        local timeout = 0
        while not organStatus and timeout < 50 do -- 5秒超时
            Wait(100)
            timeout = timeout + 1
        end
        
        if not organStatus then
            ESX.ShowNotification('获取器官状态失败', 'error')
            return
        end
        
        -- 创建器官选择菜单
        ShowOrganSelectionMenu(targetId, organStatus)
    end)
end)

-- 显示器官选择菜单 (使用HTML UI)
function ShowOrganSelectionMenu(targetId, organStatus)
    -- 使用HTML UI显示器官选择界面
    OpenOrganSelectionUI(targetId, organStatus)
end

-- 开始提取动画
RegisterNetEvent('organ_trade:startExtraction', function(duration)
    local playerPed = PlayerPedId()

    -- 开始血腥视觉效果
    StartBloodyVisualEffects()

    -- 显示进度条（包含动画）
    lib.progressBar({
        duration = duration,
        label = '正在提取器官...',
        useWhileDead = false,
        canCancel = false,
        disable = {
            car = true,
            move = true,
            combat = true
        },
        anim = {
            dict = 'mini@repair',
            clip = 'fixing_a_ped'
        }
    })

    -- 确保控制在完成后正确恢复
    CreateThread(function()
        Wait(duration + 100) -- 稍微延迟以确保进度条完成

        -- 停止血腥视觉效果
        StopBloodyVisualEffects()

        -- 确保控制已恢复（防止卡住）
        EnableAllControlActions(0)
    end)
end)

-- 检查手术状态
function CheckSurgeryStatus()
    TriggerServerEvent('organ_trade:checkSurgeryStatus')
end

-- 接收手术状态响应
RegisterNetEvent('organ_trade:surgeryStatusResponse', function(inSurgery)
    isInSurgery = inSurgery
end)

-- 创建解剖地点标记
CreateThread(function()
    for _, location in ipairs(Config.Surgery.locations) do
        local blip = AddBlipForCoord(location.x, location.y, location.z)
        SetBlipSprite(blip, 310)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, 0.8)
        SetBlipColour(blip, 1)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString(location.name)
        EndTextCommandSetBlipName(blip)
    end
end)

-- 玩家生成时检查状态
AddEventHandler('playerSpawned', function()
    CreateThread(function()
        Wait(2000)
        CheckSurgeryStatus()
    end)
end)

-- 处理器官状态刷新（用于器官摘除后更新UI而不关闭界面）
RegisterNetEvent('organ_trade:refreshOrganUI', function(organs)
    -- 如果UI界面是打开的，刷新器官状态
    SendNUIMessage({
        type = 'refreshOrganStatus',
        organStatus = organs
    })
end)



-- 导出函数
exports('UseSurgeryKnife', UseSurgeryKnife)
exports('IsInSurgery', function() return isInSurgery end)

-- 血腥视觉效果相关变量
local bloodyEffectActive = false
local bloodyEffectThread = nil

-- 开始血腥视觉效果
function StartBloodyVisualEffects()
    if bloodyEffectActive then return end
    if not Config.Surgery.visual_effects.enable_blood_effects then return end

    bloodyEffectActive = true
    local playerPed = PlayerPedId()

    -- 播放手术音效
    if Config.Surgery.visual_effects.enable_sound_effects then
        PlaySurgerySound()
    end

    -- 添加血液粒子效果
    CreateBloodyParticleEffects()

    -- 开始屏幕血腥效果
    bloodyEffectThread = CreateThread(function()
        local effectIntensity = 0.0
        local maxIntensity = Config.Surgery.visual_effects.blood_intensity or 0.6
        local fadeInSpeed = 0.02

        -- 渐进式血腥效果
        while bloodyEffectActive and effectIntensity < maxIntensity do
            effectIntensity = effectIntensity + fadeInSpeed

            -- 应用红色血腥滤镜
            SetTimecycleModifier('MP_corona_switch')
            SetTimecycleModifierStrength(effectIntensity)

            -- 轻微的屏幕晃动
            if math.random(1, 10) == 1 then
                local shakeIntensity = (Config.Surgery.visual_effects.shake_intensity or 0.5) * 0.6
                ShakeGameplayCam('HAND_SHAKE', shakeIntensity)
            end

            Wait(50)
        end

        -- 保持血腥效果
        while bloodyEffectActive do
            -- 随机的血液滴落效果
            if math.random(1, 100) == 1 then
                CreateBloodDropEffect()
            end

            -- 偶尔的强烈晃动
            if math.random(1, 200) == 1 then
                ShakeGameplayCam('HAND_SHAKE', 0.8)
                Wait(200)
            end

            Wait(100)
        end
    end)
end

-- 停止血腥视觉效果
function StopBloodyVisualEffects()
    if not bloodyEffectActive then return end

    bloodyEffectActive = false

    if bloodyEffectThread then
        bloodyEffectThread = nil
    end

    -- 渐进式清除效果
    CreateThread(function()
        local strength = 0.6
        while strength > 0 do
            SetTimecycleModifier('MP_corona_switch')
            SetTimecycleModifierStrength(strength)
            strength = strength - 0.05
            Wait(100)
        end

        -- 完全清除效果
        ClearTimecycleModifier()
        StopGameplayCamShaking(true)
    end)
end

-- 创建血液粒子效果
function CreateBloodyParticleEffects()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    -- 请求粒子效果字典
    RequestNamedPtfxAsset('core')
    while not HasNamedPtfxAssetLoaded('core') do
        Wait(100)
    end

    -- 创建血液飞溅效果
    CreateThread(function()
        local particleCount = Config.Surgery.visual_effects.particle_count or 3
        for i = 1, particleCount do
            if bloodyEffectActive then
                local offsetX = math.random(-100, 100) / 100.0
                local offsetY = math.random(-100, 100) / 100.0
                local offsetZ = math.random(0, 50) / 100.0

                UseParticleFxAssetNextCall('core')
                StartParticleFxNonLoopedAtCoord(
                    'blood_stab',
                    coords.x + offsetX,
                    coords.y + offsetY,
                    coords.z + offsetZ,
                    0.0, 0.0, 0.0,
                    1.0,
                    false, false, false
                )

                Wait(500)
            end
        end
    end)
end

-- 创建血液滴落效果
function CreateBloodDropEffect()
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    if HasNamedPtfxAssetLoaded('core') then
        UseParticleFxAssetNextCall('core')
        StartParticleFxNonLoopedAtCoord(
            'blood_stab',
            coords.x + math.random(-50, 50) / 100.0,
            coords.y + math.random(-50, 50) / 100.0,
            coords.z + 0.5,
            0.0, 0.0, 0.0,
            0.5,
            false, false, false
        )
    end
end

-- 播放手术音效
function PlaySurgerySound()
    -- 播放切割音效
    PlaySoundFrontend(-1, 'KNIFE_SLASH', 'MELEE_1_SOUNDS', 1)

    -- 延迟播放额外音效
    CreateThread(function()
        Wait(2000)
        if bloodyEffectActive then
            PlaySoundFrontend(-1, 'STAB_FLESH', 'MELEE_1_SOUNDS', 1)
        end

        Wait(3000)
        if bloodyEffectActive then
            PlaySoundFrontend(-1, 'KNIFE_SLASH', 'MELEE_1_SOUNDS', 1)
        end
    end)
end

print('^2[器官交易系统] ^7解剖系统客户端已加载')
