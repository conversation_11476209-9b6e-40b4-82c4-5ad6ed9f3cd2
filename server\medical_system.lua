-- 医疗救治系统服务器端



-- 传送到医院
RegisterNetEvent('organ_trade:transportToHospital', function(callId, victimId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xVictim = ESX.GetPlayerFromId(victimId)
    
    if not xPlayer or not xVictim then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end
    
    -- 检查权限
    if not HasPermission(source, 'medical_response') then
        NotifyPlayer(source, '你没有权限传送患者', 'error')
        return
    end
    
    -- 验证求救记录
    local rescue = exports['qiguan_tudou']:GetActiveRescue(callId)
    if not rescue then
        NotifyPlayer(source, '无效的救援记录', 'error')
        return
    end
    
    -- 使用默认医院位置（中央医院）
    local defaultHospital = {x = 298.67, y = -584.23, z = 43.26}
    
    -- 传送双方到医院
    TriggerClientEvent('organ_trade:teleportToHospital', source, defaultHospital)
    TriggerClientEvent('organ_trade:teleportToHospital', victimId, defaultHospital)

    -- 通知
    NotifyPlayer(source, '已将患者送往医院', 'success')
    NotifyPlayer(victimId, '你被送往了医院', 'info')

    -- 完成救援
    exports['qiguan_tudou']:CompleteRescue(callId, true)

    -- 清除医护人员的救援标记（已传送到医院）
    TriggerClientEvent('organ_trade:clearRescueWaypoint', source)

    -- 记录日志
    LogAction('TRANSPORTED_TO_HOSPITAL', source, victimId, '医院')
end)

-- 使用手术台治疗
RegisterNetEvent('organ_trade:useSurgeryTable', function(targetId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = targetId and ESX.GetPlayerFromId(targetId) or xPlayer
    
    if not xPlayer or not xTarget then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end
    
    -- 检查权限
    if not HasPermission(source, 'medical_response') then
        NotifyPlayer(source, '你没有权限使用手术台', 'error')
        return
    end
    
    -- 检查是否在手术台附近
    local isNear, location = IsPlayerNearLocation(source, Config.Medical.surgery_table, 3.0)
    if not isNear then
        NotifyPlayer(source, '你必须在手术台附近才能进行治疗', 'error')
        return
    end
    
    -- 获取目标玩家的器官状态
    local targetIdentifier = GetPlayerIdentifierByServerId(xTarget.source)
    local organs = GetPlayerOrgans(targetIdentifier)
    
    -- 直接打开手术台UI
    TriggerClientEvent('organ_trade:openSurgeryTableUI', source, xTarget.source, organs)
end)

-- 修复器官
RegisterNetEvent('organ_trade:repairOrgan', function(targetId, organField)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(targetId)

    print(string.format('^3[器官修复] ^7收到修复请求 - 医生: %d, 患者: %d, 器官: %s', source, targetId, organField))
    
    if not xPlayer or not xTarget then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end
    
    -- 检查权限
    if not HasPermission(source, 'medical_response') then
        NotifyPlayer(source, '你没有权限进行器官修复', 'error')
        return
    end

    -- 检查是否在手术台附近
    local isNear, location = IsPlayerNearLocation(source, Config.Medical.surgery_table, 3.0)
    if not isNear then
        NotifyPlayer(source, '你必须在手术台附近才能进行治疗', 'error')
        return
    end
    
    -- 获取对应的医用器官物品名称
    local medicalItem = GetMedicalOrganItem(organField)

    -- 检查是否有对应的医用器官物品
    if not HasPlayerItem(source, medicalItem, 1) then
        local medicalItemName = GetMedicalOrganItemName(medicalItem)
        NotifyPlayer(source, string.format('你没有%s，无法进行修复', medicalItemName), 'error')
        return
    end
    
    -- 获取器官名称
    local organName = '未知器官'
    for _, organ in pairs(Config.Surgery.organs) do
        if organ.item == organField then
            organName = organ.name
            break
        end
    end

    -- 开始修复过程（显示进度条）
    TriggerClientEvent('organ_trade:startOrganRepair', source, Config.Surgery.surgery_time, organName)

    CreateThread(function()
        Wait(Config.Surgery.surgery_time)

        -- 移除医用器官物品
        local medicalItemToRemove = GetMedicalOrganItem(organField)
        RemovePlayerItem(source, medicalItemToRemove, 1)

        -- 修复器官
        local targetIdentifier = GetPlayerIdentifierByServerId(targetId)
        RepairPlayerOrgan(targetIdentifier, organField)

        -- 通知
        NotifyPlayer(source, string.format(Config.Notifications.organ_repaired, organName), 'success')
        NotifyPlayer(targetId, string.format('你的%s已经被修复', organName), 'success')

        -- 刷新UI显示
        local updatedOrgans = GetPlayerOrgans(targetIdentifier)
        TriggerClientEvent('organ_trade:refreshSurgeryTableUI', source, updatedOrgans)

        -- 检查目标玩家的器官缺失状态并更新
        if updatedOrgans then
            local missingOrgans = {}
            local organFields = {
                'organ_heart', 'organ_liver', 'organ_kidney',
                'organ_lung', 'organ_pancreas', 'organ_spleen'
            }

            for _, organField in ipairs(organFields) do
                if updatedOrgans[organField] == 0 then
                    table.insert(missingOrgans, organField)
                end
            end

            -- 通知目标玩家更新器官缺失状态
            TriggerClientEvent('organ_trade:receiveOrganDeficiency', targetId, updatedOrgans, missingOrgans)

            if #missingOrgans == 0 then
                DebugPrint(string.format('玩家 %s 所有器官已修复完成', xTarget.getName()), 'medical')

                -- 所有器官都修复完成，停止生命倒计时和清除求救状态
                TriggerClientEvent('organ_trade:stopRescueCountdown', targetId)

                -- 清除该玩家的活跃求救记录
                local activeRescues = exports['qiguan_tudou']:GetAllActiveRescues()
                for callId, rescue in pairs(activeRescues) do
                    if rescue.victimId == targetId and (rescue.status == 'active' or rescue.status == 'responded' or rescue.status == 'arrived') then
                        -- 更新求救状态为已完成
                        exports['qiguan_tudou']:CompleteRescue(callId, true)

                        -- 如果有响应的医护人员，通知他们并清除标记
                        if rescue.responder and rescue.responderId then
                            local xMedic = ESX.GetPlayerFromId(rescue.responderId)
                            if xMedic then
                                NotifyPlayer(rescue.responderId, string.format('患者 %s 已完全康复，救援任务完成', xTarget.getName()), 'success')
                                -- 清除医护人员的导航标记和GPS路线
                                TriggerClientEvent('organ_trade:clearRescueWaypoint', rescue.responderId)
                                DebugPrint(string.format('已清除医护人员 %s 的救援标记（患者康复）', xMedic.getName()), 'medical')
                            end
                        end

                        DebugPrint(string.format('已清除玩家 %s 的求救记录 CallID: %s', xTarget.getName(), callId), 'medical')
                        break
                    end
                end

                -- 通知患者完全康复
                NotifyPlayer(targetId, '🎉 恭喜！你的所有器官已完全修复，生命危险解除！', 'success')
            else
                DebugPrint(string.format('玩家 %s 仍有缺失器官: %s',
                    xTarget.getName(), table.concat(missingOrgans, ', ')), 'medical')
            end
        end

        -- 记录日志
        LogAction('ORGAN_REPAIRED', source, targetId, string.format('%s - Used item: %s', organName, medicalItemToRemove))
    end)
end)



-- 获取附近需要肾上腺素治疗的玩家（只有正在求救倒计时的玩家）
RegisterNetEvent('organ_trade:getNearbyPatients', function()
    local source = source
    local sourceCoords = GetPlayerCoords(source)
    local nearbyPatients = {}

    -- 检查权限
    if not HasPermission(source, 'medical_response') then
        NotifyPlayer(source, '你没有权限使用医护功能', 'error')
        return
    end

    local allPlayers = GetOnlinePlayersExcept(source)
    local activeRescues = exports['qiguan_tudou']:GetAllActiveRescues()

    -- 调试：打印活跃求救记录
    DebugPrint('开始检测附近患者', 'medical')
    local rescueCount = 0
    for _ in pairs(activeRescues) do rescueCount = rescueCount + 1 end
    DebugPrint('活跃求救记录数量: ' .. rescueCount, 'medical')

    -- 打印所有活跃求救记录的详细信息
    for callId, rescue in pairs(activeRescues) do
        DebugPrint(string.format('求救记录 CallID: %s, Victim: %s, VictimId: %s, Status: %s',
            tostring(callId), tostring(rescue.victim), tostring(rescue.victimId), tostring(rescue.status)), 'medical')
    end

    for _, player in ipairs(allPlayers) do
        local targetCoords = GetPlayerCoords(player.id)
        local distance = GetDistance(sourceCoords, targetCoords)

        if distance <= 10.0 then
            -- 检查是否正在求救倒计时中（通过检查活跃求救记录）
            local hasActiveRescue = false

            for callId, rescue in pairs(activeRescues) do
                DebugPrint(string.format('检查求救记录 CallID: %s, VictimId: %s, Status: %s, 目标玩家ID: %s',
                    tostring(callId), tostring(rescue.victimId), tostring(rescue.status), tostring(player.id)), 'medical')

                if rescue.victimId == player.id and (rescue.status == 'active' or rescue.status == 'responded' or rescue.status == 'arrived') then
                    hasActiveRescue = true
                    DebugPrint('找到匹配的求救记录！', 'medical')
                    break
                end
            end

            if hasActiveRescue then
                table.insert(nearbyPatients, {
                    id = player.id,
                    name = player.name,
                    distance = math.floor(distance * 100) / 100
                })
                DebugPrint(string.format('添加患者: %s (距离: %.1fm)', player.name, distance), 'medical')
            else
                DebugPrint(string.format('玩家 %s 没有活跃的求救记录', player.name), 'medical')
            end
        end
    end

    DebugPrint(string.format('检测完成，找到 %d 个需要治疗的患者', #nearbyPatients), 'medical')
    TriggerClientEvent('organ_trade:showNearbyPatients', source, nearbyPatients)
end)

-- 检查手术台位置
RegisterNetEvent('organ_trade:checkSurgeryTableLocation', function()
    local source = source

    -- 检查权限
    if not HasPermission(source, 'medical_response') then
        NotifyPlayer(source, '你没有权限使用手术台', 'error')
        return
    end

    -- 检查是否在手术台附近
    local isNear, location = IsPlayerNearLocation(source, Config.Medical.surgery_table, 3.0)
    if not isNear then
        NotifyPlayer(source, '你必须在手术台附近才能使用', 'error')
        return
    end

    -- 获取附近需要治疗的患者
    local sourceCoords = GetPlayerCoords(source)
    local nearbyPatients = {}
    local allPlayers = GetOnlinePlayersExcept(source)

    for _, player in ipairs(allPlayers) do
        local targetCoords = GetPlayerCoords(player.id)
        local distance = GetDistance(sourceCoords, targetCoords)

        if distance <= 10.0 then
            table.insert(nearbyPatients, {
                id = player.id,
                name = player.name,
                distance = math.floor(distance * 100) / 100
            })
        end
    end

    TriggerClientEvent('organ_trade:receiveSurgeryTablePatients', source, nearbyPatients)
end)

-- 打开手术台UI
RegisterNetEvent('organ_trade:openSurgeryTableUI', function(targetId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = targetId and ESX.GetPlayerFromId(targetId) or xPlayer

    if not xPlayer or not xTarget then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end

    -- 检查权限
    if not HasPermission(source, 'medical_response') then
        NotifyPlayer(source, '你没有权限使用手术台', 'error')
        return
    end

    -- 检查是否在手术台附近
    local isNear, location = IsPlayerNearLocation(source, Config.Medical.surgery_table, 3.0)
    if not isNear then
        NotifyPlayer(source, '你必须在手术台附近才能进行治疗', 'error')
        return
    end

    -- 获取目标玩家的器官状态
    local targetIdentifier = GetPlayerIdentifierByServerId(xTarget.source)
    local organs = GetPlayerOrgans(targetIdentifier)

    -- 发送器官状态给客户端
    TriggerClientEvent('organ_trade:openSurgeryTableUI', source, xTarget.source, organs)

    -- 记录日志
    LogAction('SURGERY_TABLE_OPENED', source, xTarget.source, 'Surgery table UI opened')
end)

-- 使用肾上腺素
RegisterNetEvent('organ_trade:useAdrenaline', function(victimId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    local xVictim = ESX.GetPlayerFromId(victimId)

    if not xPlayer or not xVictim then
        NotifyPlayer(source, '目标玩家不存在', 'error')
        return
    end

    -- 检查权限
    if not HasPermission(source, 'medical_response') then
        NotifyPlayer(source, '你没有权限使用肾上腺素', 'error')
        return
    end

    -- 检查是否有肾上腺素
    if not HasPlayerItem(source, Config.Medical.adrenaline_item, 1) then
        NotifyPlayer(source, '你没有肾上腺素', 'error')
        return
    end

    -- 检查距离
    local medicCoords = GetPlayerCoords(source)
    local victimCoords = GetPlayerCoords(victimId)
    local distance = GetDistance(medicCoords, victimCoords)

    if distance > 5.0 then
        NotifyPlayer(source, '距离患者太远', 'error')
        return
    end

    -- 检查患者是否正在求救倒计时中
    local hasActiveRescue = false
    local activeRescues = exports['qiguan_tudou']:GetAllActiveRescues()

    for _, rescue in pairs(activeRescues) do
        if rescue.victimId == victimId and (rescue.status == 'active' or rescue.status == 'responded' or rescue.status == 'arrived') then
            hasActiveRescue = true
            break
        end
    end

    if not hasActiveRescue then
        NotifyPlayer(source, '患者没有发起求救信号，无法使用肾上腺素', 'error')
        return
    end

    -- 开始肾上腺素注射过程（显示进度条）
    TriggerClientEvent('organ_trade:startAdrenalineInjection', source, Config.Surgery.surgery_time)

    CreateThread(function()
        Wait(Config.Surgery.surgery_time)

        -- 移除肾上腺素
        RemovePlayerItem(source, Config.Medical.adrenaline_item, 1)

        -- 通知双方
        NotifyPlayer(source, Config.Notifications.adrenaline_used, 'success')
        NotifyPlayer(victimId, Config.Notifications.adrenaline_received, 'success')

        -- 延长求救倒计时时间
        DebugPrint(string.format('发送延长倒计时事件给玩家 %d，延长时间: %dms', victimId, Config.Medical.extend_time), 'medical')
        TriggerClientEvent('organ_trade:extendRescueCountdown', victimId, Config.Medical.extend_time)

        -- 记录日志
        LogAction('ADRENALINE_USED', source, victimId, string.format('Extended countdown by: %dms', Config.Medical.extend_time))
    end)
end)

-- 获取医用器官物品名称
function GetMedicalOrganItem(organField)
    local medicalItems = {
        ['organ_heart'] = 'medical_heart',
        ['organ_liver'] = 'medical_liver',
        ['organ_kidney'] = 'medical_kidney',
        ['organ_lung'] = 'medical_lung',
        ['organ_pancreas'] = 'medical_pancreas',
        ['organ_spleen'] = 'medical_spleen'
    }
    return medicalItems[organField] or organField
end

-- 获取医用器官物品的显示名称
function GetMedicalOrganItemName(medicalItem)
    local itemNames = {
        ['medical_heart'] = '医用心脏',
        ['medical_liver'] = '医用肝脏',
        ['medical_kidney'] = '医用肾脏',
        ['medical_lung'] = '医用肺部',
        ['medical_pancreas'] = '医用胰腺',
        ['medical_spleen'] = '医用脾脏'
    }
    return itemNames[medicalItem] or medicalItem
end

print('^2[器官交易系统] ^7医疗救治系统模块已加载')
