Config = {}

-- 基础配置
Config.Locale = 'zh'
Config.Debug = false  -- 调试模式（问题已解决，可关闭）
Config.DebugRescue = false  -- 专门的求救系统调试（可根据需要开启）

-- 迷药系统配置
Config.Drug = {
    item = 'drug_knockout',           -- 迷药道具名称
    duration = 30000,                 -- 迷晕持续时间(毫秒)
    effect_delay = 5000,              -- 药效发作延迟(毫秒)
    use_distance = 3.0               -- 使用距离
}

-- 解剖系统配置
Config.Surgery = {
    tool_item = 'surgery_knife',      -- 手术刀道具名称
    locations = {                     -- 解剖地点
        {x = 123.45, y = -678.90, z = 28.30, name = '废弃仓库'},
        {x = 234.56, y = -789.01, z = 35.40, name = '地下室'}
    },
    organs = {                        -- 器官配置
        A = {name = '心脏', item = 'organ_heart', price = 50000},
        B = {name = '肝脏', item = 'organ_liver', price = 30000},
        C = {name = '肾脏', item = 'organ_kidney', price = 25000},
        D = {name = '肺部', item = 'organ_lung', price = 20000},
        F = {name = '胰腺', item = 'organ_pancreas', price = 18000},
        G = {name = '脾脏', item = 'organ_spleen', price = 12000}
    },
    cooldown = 1000,                  -- 解剖冷却时间(1小时)
    surgery_time = 10000,             -- 解剖时间(毫秒)
    visual_effects = {                -- 视觉效果配置
        enable_blood_effects = true,  -- 启用血腥效果
        enable_medical_effects = true, -- 启用医疗效果
        enable_sound_effects = true,  -- 启用音效
        enable_victim_pain = true,    -- 启用受害者痛苦效果
        blood_intensity = 0.6,        -- 血腥效果强度 (0.0-1.0)
        medical_intensity = 0.4,      -- 医疗效果强度 (0.0-1.0)
        victim_pain_intensity = 0.8,  -- 受害者痛苦效果强度 (0.0-1.0)
        particle_count = 3,           -- 粒子效果数量 (1-5)
        shake_intensity = 0.5         -- 屏幕晃动强度 (0.0-2.0)
    }
}

-- 求救系统配置
Config.Rescue = {
    command = 'sos',                 -- 求救命令
    response_time = 300000,          -- 医护响应时间(5分钟，毫秒)
    response_timeout = 30000,        -- 按E响应的超时时间(30秒，毫秒)
    arrival_distance = 15.0,         -- 到达距离阈值(米) - 增加到15米便于触发
    death_countdown = 300000,        -- 死亡倒计时时间(5分钟，毫秒)
    cooldown = 600000,               -- 求救冷却时间(10分钟，毫秒) 
    allow_all_players = false,        -- 是否允许所有玩家发送求救信号（true=测试模式，false=只有手术受害者）
    jobs = {'ambulance', 'doctor', 'ems', 'medic'},  -- 可响应的职业
    blip = {
        sprite = 61,
        color = 1,
        scale = 1.0
    }
}

-- 医疗系统配置
Config.Medical = {
    adrenaline_item = 'adrenaline',  -- 肾上腺素道具
    extend_time = 600000,            -- 延长生命时间(10分钟)
    surgery_table = {                -- 手术台位置
        {x = 302.45, y = -582.11, z = 43.26},
        {x = 1841.2, y = 3670.45, z = 34.28}
    }
}

-- 通知配置
Config.Notifications = {
    drug_used = '你使用了迷药',
    drug_effect = '药效开始发作...',
    victim_drugged = '你感到头晕目眩，身体失去控制...',
    victim_collapsed = '你倒在了地上，只能缓慢爬行...',
    crawling_hint = '按住移动键可以缓慢爬行',
    target_already_drugged = '该玩家已被迷晕，无法重复使用迷药',
    user_drugged_cannot_use = '你处于迷晕状态，无法使用迷药',
    surgery_started = '开始解剖手术',
    organ_extracted = '成功摘除%s',
    organ_removed_victim = '你的%s被摘除了，你感到身体不适...',
    sos_sent = '求救信号已发送，等待医护人员响应',
    medic_notified = '医护人员已收到求救信号',
    medic_responded = '医护人员 %s 已接受你的求救，正在赶来救援',
    rescue_responded_to_others = '该求救信号已被其他医护人员响应',
    medic_arrived = '医护人员 %s 已到达现场！',
    medic_arrived_self = '你已到达现场，可以使用肾上腺素救治受害者',
    countdown_started = '你将在%d分钟后死亡，请等待医护人员救治！', -- 
    countdown_stopped = '医护人员已为你提供治疗，生命危险解除',
    countdown_expired = '你因为没有及时得到救治而死亡',
    adrenaline_used = '使用肾上腺素延长生命',
    adrenaline_received = '医护人员给你注射了肾上腺素，你的生命得到延长',
    surgery_completed = '手术治疗完成',
    organ_repaired = '器官修复完成: %s',
    cooldown_active = '该玩家在冷却时间内，无法进行解剖',
    player_moved = '已将玩家移动到指定位置',
    surgery_ended = '手术已结束',
    surgery_ended_victim = '手术结束，你可以发送求救信号',
    continue_surgery = '可以继续提取其他器官',
    all_organs_extracted = '所有器官已提取完毕，手术结束'
}

-- 按键配置
Config.Keys = {
    organ_trade_menu = 166,          -- F5 - 器官交易菜单
    medical_menu = 167               -- F6 - 医护系统菜单
}

-- 指令配置
Config.Commands = {
    organ_trade_menu = 'organtrade',  -- 器官交易菜单指令
    medical_menu = 'medical'          -- 医护系统菜单指令
}

-- NPC配置
Config.NPCs = {
    -- 医疗用品商店NPC
    medical_shop = {
        enabled = true,              -- 是否启用医疗用品商店
        coords = {x = 299.2502, y = -647.0001, z = 29.3288}, -- NPC位置
        heading = 160.0,             -- NPC朝向
        model = 's_m_m_doctor_01',   -- NPC模型
        name = '医疗用品商',          -- NPC名称
        blip = {
            enabled = true,          -- 是否显示地图标记
            sprite = 61,             -- 地图标记图标
            color = 2,               -- 地图标记颜色
            scale = 0.8,             -- 地图标记大小
            name = '医疗用品商店'      -- 地图标记名称
        },
        items = {                    -- 出售的物品
            {item = 'organ_heart', price = 100000, label = '心脏移植包'},
            {item = 'organ_liver', price = 60000, label = '肝脏移植包'},
            {item = 'organ_kidney', price = 50000, label = '肾脏移植包'},
            {item = 'organ_lung', price = 40000, label = '肺部移植包'},
            {item = 'organ_pancreas', price = 36000, label = '胰腺移植包'},
            {item = 'organ_spleen', price = 24000, label = '脾脏移植包'},
            {item = 'adrenaline', price = 5000, label = '肾上腺素'}
        },
        jobs = {'ambulance', 'doctor'} -- 可购买的职业
    },

    -- 器官贩卖商NPC
    organ_dealer = {
        enabled = true,              -- 是否启用器官贩卖商
        coords = {x = 1047.4714, y = -1747.0232, z = 35.5283}, -- NPC位置（偏僻地点）
        heading = 120.0,             -- NPC朝向
        model = 'g_m_y_lost_01',     -- NPC模型（看起来像黑市商人）
        name = '器官贩卖商',          -- NPC名称
        blip = {
            enabled = false,         -- 不显示地图标记（黑市）
            sprite = 280,            -- 地图标记图标
            color = 1,               -- 地图标记颜色
            scale = 0.6,             -- 地图标记大小
            name = '器官贩卖商'        -- 地图标记名称
        },
        sell_prices = {              -- 收购价格（使用器官配置中的价格）
            organ_heart = 50000,
            organ_liver = 30000,
            organ_kidney = 25000,
            organ_lung = 20000,
            organ_pancreas = 18000,
            organ_spleen = 12000
        }
    }
}

-- 警察通知系统配置
Config.PoliceAlert = {
    enabled = true,                  -- 是否启用警察通知系统
    jobs = {'police', 'sheriff'},    -- 警察职业列表
    blip_duration = 60000,           -- 标记点持续时间(毫秒) - 1分钟
    blip_flash_duration = 10000,     -- 闪烁持续时间(毫秒) - 10秒
    surgery_alert = {
        enabled = true,              -- 是否在解剖时通知警察
        title = '🚨 非法解剖活动',
        message = '检测到可疑的解剖活动',
        blip_color = 1,              -- 红色
        blip_sprite = 310            -- 解剖图标
    },
    organ_sale_alert = {
        enabled = true,              -- 是否在出售器官时通知警察
        title = '🚨 非法器官交易',
        message = '检测到器官黑市交易',
        blip_color = 1,              -- 红色
        blip_sprite = 280            -- 交易图标
    }
}

-- 权限配置
Config.Permissions = {
    use_drug = {},                   -- 可使用迷药的职业/帮派 (空表示所有玩家)
    perform_surgery = {},            -- 可进行解剖的职业/帮派 (空表示所有玩家)
    medical_response = {'ambulance', 'doctor'} -- 医疗响应职业
}
