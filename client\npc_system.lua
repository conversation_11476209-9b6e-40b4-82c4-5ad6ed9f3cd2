-- NPC系统客户端
local spawnedNPCs = {}

-- 创建NPC
function CreateNPC(npcConfig, npcType)
    local model = GetHashKey(npcConfig.model)

    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(1)
    end

    local npc = CreatePed(4, model, npcConfig.coords.x, npcConfig.coords.y, npcConfig.coords.z - 1.0, npcConfig.heading, false, true)

    SetEntityHeading(npc, npcConfig.heading)
    FreezeEntityPosition(npc, true)
    SetEntityInvincible(npc, true)
    SetBlockingOfNonTemporaryEvents(npc, true)

    -- 添加ox_target交互
    if npcType == 'medical_shop' then
        exports.ox_target:addLocalEntity(npc, {
            {
                name = 'medical_shop_interact',
                icon = 'fa-solid fa-shopping-cart',
                label = '医疗用品商店',
                onSelect = function()
                    TriggerServerEvent('organ_trade:openMedicalShop')
                end
            }
        })
    elseif npcType == 'organ_dealer' then
        exports.ox_target:addLocalEntity(npc, {
            {
                name = 'organ_dealer_interact',
                icon = 'fa-solid fa-hand-holding-dollar',
                label = '器官贩卖商',
                onSelect = function()
                    TriggerServerEvent('organ_trade:openOrganDealer')
                end
            }
        })
    end

    -- 创建地图标记
    if npcConfig.blip.enabled then
        local blip = AddBlipForCoord(npcConfig.coords.x, npcConfig.coords.y, npcConfig.coords.z)
        SetBlipSprite(blip, npcConfig.blip.sprite)
        SetBlipDisplay(blip, 4)
        SetBlipScale(blip, npcConfig.blip.scale)
        SetBlipColour(blip, npcConfig.blip.color)
        SetBlipAsShortRange(blip, true)
        BeginTextCommandSetBlipName('STRING')
        AddTextComponentString(npcConfig.blip.name)
        EndTextCommandSetBlipName(blip)

        spawnedNPCs[npcType] = {ped = npc, blip = blip}
    else
        spawnedNPCs[npcType] = {ped = npc, blip = nil}
    end

    SetModelAsNoLongerNeeded(model)

    if Config.Debug then
        print(string.format('^2[NPC系统] ^7创建NPC: %s 在坐标 %.2f, %.2f, %.2f', npcConfig.name, npcConfig.coords.x, npcConfig.coords.y, npcConfig.coords.z))
    end
end

-- 初始化所有NPC
CreateThread(function()
    Wait(5000) -- 等待游戏完全加载
    
    -- 创建医疗用品商店NPC
    if Config.NPCs.medical_shop.enabled then
        CreateNPC(Config.NPCs.medical_shop, 'medical_shop')
    end
    
    -- 创建器官贩卖商NPC
    if Config.NPCs.organ_dealer.enabled then
        CreateNPC(Config.NPCs.organ_dealer, 'organ_dealer')
    end
end)



-- 打开医疗用品商店菜单
RegisterNetEvent('organ_trade:showMedicalShop', function(items, playerJob)
    local options = {}

    for _, item in ipairs(items) do
        table.insert(options, {
            title = item.label,
            description = string.format('价格: $%s 每个', item.price),
            icon = 'fa-solid fa-pills',
            onSelect = function()
                ShowQuantityInput(item, 'buy')
            end
        })
    end

    lib.registerContext({
        id = 'medical_shop',
        title = '🏥 医疗用品商店',
        options = options
    })

    lib.showContext('medical_shop')
end)

-- 显示数量输入对话框
function ShowQuantityInput(item, action)
    local input = lib.inputDialog('选择数量', {
        {
            type = 'number',
            label = string.format('购买 %s 的数量', item.label),
            description = string.format('单价: $%s', item.price),
            required = true,
            min = 1,
            max = 50,
            default = 1
        }
    })

    if input and input[1] then
        local quantity = tonumber(input[1])
        if quantity and quantity > 0 then
            if action == 'buy' then
                TriggerServerEvent('organ_trade:buyMedicalItem', item.item, item.price, quantity)
            end
        else
            lib.notify({
                title = '错误',
                description = '请输入有效的数量',
                type = 'error'
            })
        end
    end
end

-- 打开器官贩卖商菜单
RegisterNetEvent('organ_trade:showOrganDealer', function(sellPrices, playerItems)
    local options = {}

    for organItem, price in pairs(sellPrices) do
        local organName = GetOrganNameByItem(organItem)
        local quantity = playerItems[organItem] or 0

        table.insert(options, {
            title = string.format('出售 %s', organName),
            description = string.format('价格: $%s 每个 | 拥有: %d 个', price, quantity),
            icon = 'fa-solid fa-heart',
            onSelect = function()
                ShowOrganSellQuantityInput(organItem, organName, price, quantity)
            end
        })
    end

    if #options == 0 then
        table.insert(options, {
            title = '没有可出售的器官',
            description = '你的背包中没有器官物品',
            icon = 'fa-solid fa-exclamation-triangle'
        })
    end

    lib.registerContext({
        id = 'organ_dealer',
        title = '🖤 器官贩卖商',
        options = options
    })

    lib.showContext('organ_dealer')
end)

-- 显示器官出售数量输入
function ShowOrganSellQuantityInput(organItem, organName, price, maxQuantity)
    local input = lib.inputDialog('选择出售数量', {
        {
            type = 'number',
            label = string.format('出售 %s 的数量', organName),
            description = string.format('单价: $%s | 最多可出售: %d 个', price, maxQuantity),
            required = true,
            min = 1,
            max = maxQuantity,
            default = 1
        }
    })

    if input and input[1] then
        local quantity = tonumber(input[1])
        if quantity and quantity > 0 and quantity <= maxQuantity then
            TriggerServerEvent('organ_trade:sellOrgan', organItem, price, quantity)
        else
            lib.notify({
                title = '错误',
                description = '请输入有效的数量',
                type = 'error'
            })
        end
    end
end

-- 根据物品名获取器官名称
function GetOrganNameByItem(item)
    for _, organ in pairs(Config.Surgery.organs) do
        if organ.item == item then
            return organ.name
        end
    end
    return item
end

-- 清理NPC
AddEventHandler('onResourceStop', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        for npcType, npcData in pairs(spawnedNPCs) do
            if DoesEntityExist(npcData.ped) then
                -- 移除ox_target交互
                exports.ox_target:removeLocalEntity(npcData.ped)
                DeleteEntity(npcData.ped)
            end
            if npcData.blip and DoesBlipExist(npcData.blip) then
                RemoveBlip(npcData.blip)
            end
        end
    end
end)

print('^2[器官交易系统] ^7NPC系统客户端已加载')
