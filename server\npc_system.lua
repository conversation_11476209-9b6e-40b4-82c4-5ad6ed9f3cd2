-- NPC系统服务器端

-- 打开医疗用品商店
RegisterNetEvent('organ_trade:openMedicalShop', function()
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then
        return
    end
    
    -- 检查职业权限
    local playerJob = xPlayer.job.name
    local hasPermission = false
    
    for _, job in ipairs(Config.NPCs.medical_shop.jobs) do
        if playerJob == job then
            hasPermission = true
            break
        end
    end
    
    if not hasPermission then
        NotifyPlayer(source, '只有医护人员才能购买医疗用品', 'error')
        return
    end
    
    -- 发送商店物品列表
    TriggerClientEvent('organ_trade:showMedicalShop', source, Config.NPCs.medical_shop.items, playerJob)
end)

-- 购买医疗物品
RegisterNetEvent('organ_trade:buyMedicalItem', function(item, price, quantity)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        return
    end

    -- 验证数量
    quantity = quantity or 1
    if quantity < 1 or quantity > 50 then
        NotifyPlayer(source, '无效的购买数量', 'error')
        return
    end

    -- 检查职业权限
    local playerJob = xPlayer.job.name
    local hasPermission = false

    for _, job in ipairs(Config.NPCs.medical_shop.jobs) do
        if playerJob == job then
            hasPermission = true
            break
        end
    end

    if not hasPermission then
        NotifyPlayer(source, '只有医护人员才能购买医疗用品', 'error')
        return
    end

    -- 计算总价
    local totalPrice = price * quantity

    -- 检查金钱
    if xPlayer.getMoney() < totalPrice then
        NotifyPlayer(source, string.format('你的现金不足，需要 $%s', totalPrice), 'error')
        return
    end

    -- 扣除金钱并给予物品
    xPlayer.removeMoney(totalPrice)
    GivePlayerItem(source, item, quantity)

    -- 获取物品名称
    local itemLabel = item
    for _, shopItem in ipairs(Config.NPCs.medical_shop.items) do
        if shopItem.item == item then
            itemLabel = shopItem.label
            break
        end
    end

    NotifyPlayer(source, string.format('购买了 %d 个 %s，花费 $%s', quantity, itemLabel, totalPrice), 'success')

    -- 记录日志
    LogAction('MEDICAL_ITEM_PURCHASED', source, nil, string.format('%s x%d - Total: $%s', itemLabel, quantity, totalPrice))

    if Config.Debug then
        print(string.format('^3[医疗商店] ^7玩家 %s 购买了 %d 个 %s，总价: $%s', xPlayer.getName(), quantity, itemLabel, totalPrice))
    end
end)

-- 打开器官贩卖商
RegisterNetEvent('organ_trade:openOrganDealer', function()
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        return
    end

    -- 检查玩家背包中的器官物品和数量
    local playerOrgans = {}
    local playerItems = {}

    for organItem, price in pairs(Config.NPCs.organ_dealer.sell_prices) do
        local itemCount = GetPlayerItemCount(source, organItem)
        if itemCount > 0 then
            playerOrgans[organItem] = price
            playerItems[organItem] = itemCount
        end
    end

    -- 发送可出售的器官列表和数量
    TriggerClientEvent('organ_trade:showOrganDealer', source, playerOrgans, playerItems)
end)

-- 获取玩家物品数量
function GetPlayerItemCount(source, item)
    local xPlayer = ESX.GetPlayerFromId(source)
    if not xPlayer then return 0 end

    local playerItem = xPlayer.getInventoryItem(item)
    return playerItem and playerItem.count or 0
end

-- 出售器官
RegisterNetEvent('organ_trade:sellOrgan', function(organItem, price, quantity)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then
        return
    end

    -- 验证数量
    quantity = quantity or 1
    if quantity < 1 then
        NotifyPlayer(source, '无效的出售数量', 'error')
        return
    end

    -- 检查是否有足够的器官
    local itemCount = GetPlayerItemCount(source, organItem)
    if itemCount < quantity then
        NotifyPlayer(source, string.format('你只有 %d 个这种器官', itemCount), 'error')
        return
    end

    -- 验证价格
    if Config.NPCs.organ_dealer.sell_prices[organItem] ~= price then
        NotifyPlayer(source, '价格验证失败', 'error')
        return
    end

    -- 计算总价
    local totalPrice = price * quantity

    -- 移除器官并给予金钱
    RemovePlayerItem(source, organItem, quantity)
    xPlayer.addMoney(totalPrice)

    -- 获取器官名称
    local organName = organItem
    for _, organ in pairs(Config.Surgery.organs) do
        if organ.item == organItem then
            organName = organ.name
            break
        end
    end

    -- 发送警察通知
    TriggerEvent('organ_trade:organSold', source, organItem, quantity)

    NotifyPlayer(source, string.format('出售了 %d 个 %s，获得 $%s', quantity, organName, totalPrice), 'success')

    -- 记录日志
    LogAction('ORGAN_SOLD', source, nil, string.format('%s x%d - Total: $%s', organName, quantity, totalPrice))

    if Config.Debug then
        print(string.format('^3[器官贩卖] ^7玩家 %s 出售了 %d 个 %s，总价: $%s', xPlayer.getName(), quantity, organName, totalPrice))
    end
end)

print('^2[器官交易系统] ^7NPC系统服务器端已加载')
