<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>器官交易系统测试</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 器官选择界面 -->
    <div id="organSelection" class="ui-panel" style="display: block;">
        <div class="panel-header">
            <h2>人体器官解剖</h2>
            <button class="close-btn" onclick="cancelSurgery()">×</button>
        </div>
        
        <div class="panel-content">
            <div class="human-body">
                <img src="images/human_body.png" alt="人体图" class="body-image">
                
                <!-- 器官选择按钮 - 根据新人体图精确定位 -->
                <div class="organ-buttons">
                    <!-- A: 心脏 - 胸部中央偏右 -->
                    <button class="organ-btn" data-organ="A" style="top: 28%; left: 55%;" title="心脏">A</button>

                    <!-- B: 肝脏 - 右上腹部 -->
                    <button class="organ-btn" data-organ="B" style="top: 38%; left: 35%;" title="肝脏">B</button>

                    <!-- C: 肾脏 - 左下腹部 -->
                    <button class="organ-btn" data-organ="C" style="top: 55%; left: 25%;" title="肾脏">C</button>

                    <!-- D: 肺部 - 左上胸部 -->
                    <button class="organ-btn" data-organ="D" style="top: 25%; left: 35%;" title="肺部">D</button>

                    <!-- F: 胰腺 - 腹部中央 -->
                    <button class="organ-btn" data-organ="F" style="top: 45%; left: 50%;" title="胰腺">F</button>

                    <!-- G: 脾脏 - 右上腹部 -->
                    <button class="organ-btn" data-organ="G" style="top: 38%; left: 65%;" title="脾脏">G</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 通知系统 -->
    <div id="notifications" class="notifications-container"></div>
    
    <!-- 倒计时显示 -->
    <div id="countdown" class="countdown-container" style="display: none;">
        <div class="countdown-content">
            <div class="countdown-text" id="countdownText">倒计时</div>
            <div class="countdown-time" id="countdownTime">00:00</div>
        </div>
    </div>
    
    <!-- 器官状态显示 -->
    <div id="organStatus" class="organ-status-container" style="display: none;">
        <div class="organ-status-header">
            <h3>器官状态</h3>
            <button class="close-btn" onclick="hideOrganStatus()">×</button>
        </div>
        <div class="organ-status-content" id="organStatusContent">
            <!-- 器官状态将通过JavaScript动态生成 -->
        </div>
    </div>

    <script>
        // 模拟器官数据
        const testOrgans = {
            'A': { name: '心脏', price: 50000, item: 'heart' },
            'B': { name: '肝脏', price: 30000, item: 'liver' },
            'C': { name: '肾脏', price: 25000, item: 'kidney' },
            'D': { name: '肺部', price: 20000, item: 'lung' },
            'F': { name: '胰腺', price: 15000, item: 'pancreas' },
            'G': { name: '脾脏', price: 10000, item: 'spleen' }
        };

        const testOrganStatus = {
            'heart': 1,
            'liver': 1,
            'kidney': 0,
            'lung': 1,
            'pancreas': 0,
            'spleen': 1
        };

        // 设置全局变量
        window.currentOrgans = testOrgans;
        window.currentOrganStatus = testOrganStatus;

        // 模拟GetParentResourceName函数
        window.GetParentResourceName = function() {
            return 'qiguan_tudou';
        };

        // 模拟fetch函数
        window.fetch = function(url, options) {
            console.log('模拟请求:', url, options);
            return Promise.resolve({ ok: true });
        };
    </script>
    <script src="script.js"></script>
    <script>
        // 初始化测试
        document.addEventListener('DOMContentLoaded', function() {
            updateOrganButtons();
            console.log('测试页面已加载，请将鼠标悬停在器官按钮上测试提示框显示');
            console.log('已移除CSS中的箭头样式，黑色框应该消失了');
        });
    </script>
</body>
</html>
