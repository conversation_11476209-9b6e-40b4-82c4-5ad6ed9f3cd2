-- 求救系统服务器端

-- 玩家连接时检查是否有活跃的求救
AddEventHandler('esx:playerLoaded', function(playerId, xPlayer)
    local identifier = GetPlayerIdentifierByServerId(playerId)
    -- 检查该玩家是否有活跃的求救记录
    for callId, rescue in pairs(activeRescues) do
        if rescue.victim == identifier and rescue.status == 'active' then
            -- 更新玩家ID
            rescue.victimId = playerId
            print(string.format('^3[求救系统] ^7玩家 %s 重新连接，恢复求救记录 CallID: %d', xPlayer.getName(), callId))

            -- 重新启用SOS功能
            TriggerClientEvent('organ_trade:enableSOS', playerId)

            -- 通知玩家
            NotifyPlayer(playerId, '你有一个活跃的求救信号', 'info')
            break
        end
    end
end)

-- 发送SOS求救信号
RegisterNetEvent('organ_trade:sendSOS', function()
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then return end

    local identifier = GetPlayerIdentifierByServerId(source)
    local playerCoords = GetPlayerCoords(source)

    -- 检查玩家器官状态
    local organs = GetPlayerOrgans(identifier)
    if not organs then
        NotifyPlayer(source, '无法获取器官状态，请稍后再试', 'error')
        return
    end

    -- 检查是否有缺失的器官
    local missingOrgans = {}
    local organFields = {
        'organ_heart', 'organ_liver', 'organ_kidney',
        'organ_lung', 'organ_pancreas', 'organ_spleen'
    }

    for _, organField in ipairs(organFields) do
        if organs[organField] == 0 then
            table.insert(missingOrgans, organField)
        end
    end

    DebugPrint(string.format('玩家 %s 器官检查 - 缺失器官数量: %d', xPlayer.getName(), #missingOrgans), 'rescue')
    if #missingOrgans > 0 then
        DebugPrint(string.format('缺失器官: %s', table.concat(missingOrgans, ', ')), 'rescue')
    end

    -- 如果没有缺失器官，不允许求救
    if #missingOrgans == 0 then
        NotifyPlayer(source, '你的身体完好，不需要救援', 'info')
        DebugPrint(string.format('玩家 %s 身体完好，拒绝求救请求', xPlayer.getName()), 'rescue')
        return
    end

    -- 检查求救冷却时间
    if not CheckRescueCooldown(identifier) then
        local cooldownMinutes = math.ceil(Config.Rescue.cooldown / 60000)
        NotifyPlayer(source, string.format('求救信号冷却中，请等待 %d 分钟后再试', cooldownMinutes), 'error')
        DebugPrint(string.format('玩家 %s 求救冷却中，拒绝求救请求', xPlayer.getName()), 'rescue')
        return
    end

    -- 检查是否已经有活跃的求救
    for callId, rescue in pairs(activeRescues) do
        if rescue.victim == identifier and rescue.status == 'active' then
            NotifyPlayer(source, '你已经发送过求救信号', 'error')
            return
        end
    end
    
    -- 创建求救记录
    local callId = CreateRescueCall(identifier, playerCoords)
    
    if not callId then
        NotifyPlayer(source, '发送求救信号失败', 'error')
        return
    end
    
    -- 添加到活跃求救列表
    activeRescues[callId] = {
        id = callId,
        victim = identifier,
        victimId = source,
        victimName = xPlayer.getName(),
        coords = playerCoords,
        status = 'active',
        timestamp = GetGameTimer(),
        blipId = nil
    }
    
    -- 设置求救冷却时间
    SetRescueCooldown(identifier)

    -- 通知受害者
    NotifyPlayer(source, Config.Notifications.sos_sent, 'success')

    -- 启动受害者的死亡倒计时
    TriggerClientEvent('organ_trade:startRescueCountdown', source, Config.Rescue.death_countdown)

    -- 调试：记录求救信号发送
    DebugPrint(string.format('玩家 %s (%s) 发送求救信号，CallID: %d', xPlayer.getName(), identifier, callId), 'rescue')
    DebugPrint(string.format('坐标: %.2f, %.2f, %.2f', playerCoords.x, playerCoords.y, playerCoords.z), 'rescue')

    -- 注释掉重复的NotifyJob调用，因为下面的循环已经包含了通知功能
    -- local medicMessage = string.format('收到求救信号！受害者: %s', xPlayer.getName())
    -- NotifyJob(Config.Rescue.jobs, medicMessage, 'error')

    -- 调试：检查在线医护人员
    local xPlayers = ESX.GetExtendedPlayers()
    local medicCount = 0
    local notifiedCount = 0

    DebugPrint(string.format('开始检查在线医护人员，总在线玩家数: %d', #xPlayers), 'rescue')
    DebugPrint(string.format('配置的医护职业: %s', json.encode(Config.Rescue.jobs)), 'rescue')

    -- 发送求救信息给医护人员
    for _, medic in pairs(xPlayers) do
        -- 安全检查：确保玩家对象有效
        if not medic or not medic.source then
            print('^1[求救系统] ^7警告: 发现无效的玩家对象') -- 错误信息保留
            goto continue
        end

        -- 调试：显示每个玩家的职业信息
        local playerJob = 'unknown'
        if medic.job and medic.job.name then
            playerJob = medic.job.name
        end

        DebugPrint(string.format('检查玩家 %s (ID: %d) 职业: %s', medic.getName(), medic.source, playerJob), 'rescue')

        -- 检查是否是医护人员
        local isMedic = false
        for _, job in ipairs(Config.Rescue.jobs) do
            if medic.job and medic.job.name and medic.job.name == job then
                isMedic = true
                medicCount = medicCount + 1

                if Config.DebugRescue then
                    print(string.format('^2[求救系统] ^7找到医护人员: %s (ID: %d, 职业: %s)', medic.getName(), medic.source, job))
                end

                -- 发送ESX通知
                local esxNotifySuccess = pcall(function()
                    NotifyPlayer(medic.source, string.format('收到求救信号！受害者: %s，按E响应', xPlayer.getName()), 'error')
                end)

                -- 发送客户端事件（等待响应，不立即创建导航）
                local clientEventSuccess = pcall(function()
                    TriggerClientEvent('organ_trade:receiveSOSCall', medic.source, {
                        callId = callId,
                        victimName = xPlayer.getName(),
                        coords = playerCoords,
                        timestamp = os.date('%H:%M:%S')
                    })
                end)

                -- 统计成功的通知方式
                local successCount = 0
                if esxNotifySuccess then successCount = successCount + 1 end
                if clientEventSuccess then successCount = successCount + 1 end

                if successCount > 0 then
                    notifiedCount = notifiedCount + 1
                    if Config.DebugRescue then
                        print(string.format('^2[求救系统] ^7已向医护人员 %s 发送求救信号 (ESX通知: %s, 客户端事件: %s)',
                            medic.getName(), esxNotifySuccess and "成功" or "失败", clientEventSuccess and "成功" or "失败"))
                    end
                else
                    print(string.format('^1[求救系统] ^7错误: 无法向医护人员 %s 发送任何通知', medic.getName())) -- 错误信息保留
                end

                break
            end
        end

        ::continue::
    end

    -- 调试：总结通知结果
    DebugPrint(string.format('通知完成 - 找到医护人员: %d, 成功通知: %d', medicCount, notifiedCount), 'rescue')

    if medicCount == 0 then
        print('^1[求救系统] ^7警告: 没有找到任何在线的医护人员！') -- 重要警告保留
        -- 通知求救者没有医护人员在线
        NotifyPlayer(source, '当前没有医护人员在线，请稍后再试', 'error')
    end
    
    -- 设置5分钟超时
    CreateThread(function()
        Wait(Config.Rescue.response_time)
        
        if activeRescues[callId] and activeRescues[callId].status == 'active' then
            -- 超时处理
            activeRescues[callId].status = 'expired'
            UpdateRescueCall(callId, 'expired', nil)
            
            NotifyPlayer(source, '求救信号已超时，没有医护人员响应', 'error')

            -- 如果求救超时，继续倒计时直到死亡（不停止倒计时）
            -- 倒计时会在客户端自然结束并触发死亡
            
            -- 清理求救记录
            CreateThread(function()
                Wait(60000) -- 1分钟后清理
                if activeRescues[callId] then
                    activeRescues[callId] = nil
                end
            end)
        end
    end)
    
    -- 记录日志
    LogAction('SOS_SENT', source, nil, string.format('Coords: %.2f, %.2f, %.2f', playerCoords.x, playerCoords.y, playerCoords.z))
end)

-- 医护人员响应求救
RegisterNetEvent('organ_trade:respondToSOS', function(callId)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then return end
    
    -- 检查职业权限
    local hasPermission = false
    for _, job in ipairs(Config.Rescue.jobs) do
        if xPlayer.job.name == job then
            hasPermission = true
            break
        end
    end
    
    if not hasPermission then
        NotifyPlayer(source, '你没有权限响应求救', 'error')
        return
    end
    
    -- 检查求救是否存在且活跃
    if not activeRescues[callId] or activeRescues[callId].status ~= 'active' then
        NotifyPlayer(source, '该求救信号已失效', 'error')
        return
    end
    
    local rescue = activeRescues[callId]
    
    -- 更新求救状态
    rescue.status = 'responded'
    rescue.responder = GetPlayerIdentifierByServerId(source)
    rescue.responderName = xPlayer.getName()
    rescue.responderId = source

    UpdateRescueCall(callId, 'responded', GetPlayerIdentifierByServerId(source))
    
    -- 通知响应的医护人员
    NotifyPlayer(source, string.format('你已响应 %s 的求救信号，正在设置导航', rescue.victimName), 'success')

    -- 通知受害者（医护已响应，但倒计时继续）
    if rescue.victimId and ESX.GetPlayerFromId(rescue.victimId) then
        NotifyPlayer(rescue.victimId, string.format(Config.Notifications.medic_responded, xPlayer.getName()), 'success')
        -- 注意：这里不停止死亡倒计时，只有在实际接受治疗时才停止
    end

    -- 通知其他医护人员该求救已被响应
    local xPlayers = ESX.GetExtendedPlayers()
    for _, medic in pairs(xPlayers) do
        if medic.source ~= source then -- 不通知响应者自己
            for _, job in ipairs(Config.Rescue.jobs) do
                if medic.job and medic.job.name == job then
                    NotifyPlayer(medic.source, string.format('求救信号已被 %s 响应 (受害者: %s)', xPlayer.getName(), rescue.victimName), 'info')
                    -- 清除其他医护人员的待响应状态
                    TriggerClientEvent('organ_trade:clearPendingRescue', medic.source)
                    break
                end
            end
        end
    end

    -- 创建导航点给响应的医护人员
    TriggerClientEvent('organ_trade:setRescueWaypoint', source, rescue.coords, rescue.victimName)
    
    -- 记录日志
    LogAction('SOS_RESPONDED', source, rescue.victimId, string.format('CallID: %d, Victim: %s', callId, rescue.victimName))
end)

-- 旧的到达现场事件已删除，现在使用自动检测系统

-- 医护人员自动到达现场（通过坐标检测）
RegisterNetEvent('organ_trade:medicArrived', function(coords, victimName)
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then return end

    -- 检查权限
    local hasPermission = false
    for _, job in ipairs(Config.Rescue.jobs) do
        if xPlayer.job.name == job then
            hasPermission = true
            break
        end
    end

    if not hasPermission then
        NotifyPlayer(source, '你没有权限执行此操作', 'error')
        return
    end

    -- 查找对应的求救记录
    local callId = nil
    local rescue = nil

    for id, rescueData in pairs(activeRescues) do
        if rescueData.status == 'responded' and
           rescueData.responder == GetPlayerIdentifierByServerId(source) and
           rescueData.victimName == victimName then
            callId = id
            rescue = rescueData
            break
        end
    end

    if not rescue then
        NotifyPlayer(source, '未找到对应的救援记录', 'error')
        return
    end

    -- 更新状态
    rescue.status = 'arrived'

    -- 通知医护人员可以使用肾上腺素
    NotifyPlayer(source, string.format('你已到达 %s 的现场，可以使用肾上腺素救治受害者', victimName), 'success')

    -- 通知受害者
    if rescue.victimId and ESX.GetPlayerFromId(rescue.victimId) then
        NotifyPlayer(rescue.victimId, string.format(Config.Notifications.medic_arrived, xPlayer.getName()), 'success')
    end

    -- 强制清除医护人员的救援标记
    TriggerClientEvent('organ_trade:clearRescueWaypoint', source)

    -- 记录日志
    LogAction('MEDIC_AUTO_ARRIVED', source, rescue.victimId, string.format('CallID: %d, Victim: %s', callId, victimName))
end)

-- 求救倒计时过期处理
RegisterNetEvent('organ_trade:rescueCountdownExpired', function()
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)

    if not xPlayer then return end

    local identifier = GetPlayerIdentifierByServerId(source)

    -- 查找该玩家的活跃求救记录
    for callId, rescue in pairs(activeRescues) do
        if rescue.victim == identifier and (rescue.status == 'active' or rescue.status == 'responded') then
            -- 更新求救状态为过期
            rescue.status = 'expired_death'
            UpdateRescueCall(callId, 'expired_death', rescue.responder)

            -- 通知玩家死亡
            NotifyPlayer(source, Config.Notifications.countdown_expired, 'error')

            -- 如果有响应的医护人员，通知他们并清除标记
            if rescue.responder and rescue.responderId then
                local xMedic = ESX.GetPlayerFromId(rescue.responderId)
                if xMedic then
                    NotifyPlayer(rescue.responderId, string.format('患者 %s 因未及时救治而死亡', rescue.victimName), 'error')
                    -- 清除医护人员的导航标记和GPS路线
                    TriggerClientEvent('organ_trade:clearRescueWaypoint', rescue.responderId)
                    DebugPrint(string.format('已清除医护人员 %s 的救援标记（患者死亡）', xMedic.getName()), 'rescue')
                end
            end

            -- 执行死亡处理
            TriggerClientEvent('organ_trade:handlePlayerDeath', source)

            -- 清理求救记录
            CreateThread(function()
                Wait(60000) -- 1分钟后清理
                if activeRescues[callId] then
                    activeRescues[callId] = nil
                end
            end)

            -- 记录日志
            LogAction('RESCUE_COUNTDOWN_EXPIRED', source, rescue.responderId, string.format('CallID: %d, Death by timeout', callId))

            print(string.format('^1[求救系统] ^7玩家 %s 求救倒计时过期死亡', xPlayer.getName()))
            break
        end
    end
end)

-- 获取活跃的求救列表
RegisterNetEvent('organ_trade:getActiveRescues', function()
    local source = source
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if not xPlayer then return end
    
    -- 检查权限
    local hasPermission = false
    for _, job in ipairs(Config.Rescue.jobs) do
        if xPlayer.job.name == job then
            hasPermission = true
            break
        end
    end
    
    if not hasPermission then
        return
    end
    
    -- 收集活跃的求救
    local rescueList = {}
    for callId, rescue in pairs(activeRescues) do
        if rescue.status == 'active' then
            table.insert(rescueList, {
                id = callId,
                victimName = rescue.victimName,
                coords = rescue.coords,
                timestamp = rescue.timestamp,
                timeAgo = math.floor((GetGameTimer() - rescue.timestamp) / 1000)
            })
        end
    end
    
    TriggerClientEvent('organ_trade:receiveActiveRescues', source, rescueList)
end)

-- 完成救援
function CompleteRescue(callId, success)
    if not activeRescues[callId] then return end
    
    local rescue = activeRescues[callId]
    rescue.status = success and 'completed' or 'failed'
    
    UpdateRescueCall(callId, rescue.status, rescue.responder)
    
    -- 清理记录
    CreateThread(function()
        Wait(300000) -- 5分钟后清理
        if activeRescues[callId] then
            activeRescues[callId] = nil
        end
    end)
end

-- 导出函数
exports('CompleteRescue', CompleteRescue)
exports('GetActiveRescue', function(callId)
    return activeRescues[callId]
end)
exports('GetAllActiveRescues', function()
    return activeRescues
end)



print('^2[器官交易系统] ^7求救系统模块已加载')
